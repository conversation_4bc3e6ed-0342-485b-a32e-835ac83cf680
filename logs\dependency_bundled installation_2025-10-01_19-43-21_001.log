========================================
AIStudio Real-time Log: dependency_imagegeneration
Started: 2025-10-02T00:43:21.291Z
File: dependency_bundled installation_2025-10-01_19-43-21_001.log
========================================

[2025-10-02T00:43:21.291Z] [INFO] DependencyManager: Starting bundled installation for ImageGeneration
[2025-10-02T00:43:21.291Z] [INFO] DependencyManager: Starting portable installation of ImageGeneration module
[2025-10-02T00:43:21.292Z] [INFO] DependencyManager: Source: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-10-02T00:43:21.292Z] [INFO] DependencyManager: Extraction Target: N:\AIStudio\pipelines\ImageGeneration
[2025-10-02T00:43:21.292Z] [INFO] DependencyManager: Checking for existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-10-02T00:43:21.293Z] [INFO] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-10-02T00:43:21.328Z] [INFO] DependencyManager: Existing directory removed successfully
[2025-10-02T00:43:21.330Z] [INFO] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-10-02T00:43:21.331Z] [INFO] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip to N:\AIStudio\pipelines
