const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const logger = require('./logger');

// ---- Constants ----
const HOST = '127.0.0.1';
const PORT = 7961; // API server port for Hunyuan3D-2.1

// Use our existing venv but with gradio_app.py approach
const WORKSPACE_ROOT = path.resolve(__dirname, '../..');
const PIPELINE_ROOT = path.join(WORKSPACE_ROOT, 'pipelines', '3DPipelines', 'gen3d', 'Hunyuan3D-2.1');
const PORTABLE_ROOT = path.join(PIPELINE_ROOT, 'assets', 'Hunyuan3D2_WinPortable', 'Hunyuan3D-2.1');
const PORTABLE_PYTHON = path.join(PIPELINE_ROOT, 'assets', 'Hunyuan3D2_WinPortable', 'python_standalone', 'python.exe');
const VENV_PYTHON_SCRIPTS = path.join(PIPELINE_ROOT, 'venv', 'python.exe'); // legacy venv fallback
const VENV_PYTHON_ROOT = path.join(PIPELINE_ROOT, 'venv', 'python.exe');
const VENV_PIP_SCRIPTS = path.join(PIPELINE_ROOT, 'venv', 'Scripts', 'pip.exe');
const VENV_PIP_ROOT = path.join(PIPELINE_ROOT, 'venv', 'Scripts', 'pip.exe');
const API_SERVER_PATH = path.join(PORTABLE_ROOT, 'api_server.py');

const SAVE_DIR = path.join(PIPELINE_ROOT, 'save_dir');

// Use PathManager for cache and models directories
function getHunyuan21CacheDir() {
  const pathManager = require('./pathManager');
  return pathManager.getPipelineCachePath('Hunyuan3D-2.1');
}

function getHunyuan21ModelsDir() {
  const pathManager = require('./pathManager');
  const modelsPath = pathManager.getPipelineModelsPath('Hunyuan3D-2.1');
  // Ensure the models directory exists
  if (!fs.existsSync(modelsPath)) {
    fs.mkdirSync(modelsPath, { recursive: true });
  }
  return modelsPath;
}
const REQUIREMENTS_FILE = path.join(PIPELINE_ROOT, 'requirements.txt');

function log(...args) {
  try {
    const msg = args.map(a => {
      if (typeof a === 'string') return a;
      if (a instanceof Error) return a.stack || a.message;
      try { return JSON.stringify(a); } catch { return String(a); }
    }).join(' ');
    logger.info(`[Hunyuan3D-2.1] ${msg}`);
  } catch (_) {
    try { logger.info('[Hunyuan3D-2.1] (log failed)'); } catch {}
  }
}

function encodeImageToBase64(imagePath) {
  try {
    const imageBuffer = fs.readFileSync(imagePath);
    const base64String = imageBuffer.toString('base64');
    const mimeType = imagePath.toLowerCase().endsWith('.png') ? 'image/png' : 'image/jpeg';
    return `data:${mimeType};base64,${base64String}`;
  } catch (error) {
    throw new Error(`Failed to encode image to base64: ${error.message}`);
  }
}

// Emit model download progress to the Dependency Manager so the global modal shows it
function emitModelProgress(status, message, progress) {
  try {
    const { dependencyManager } = require('./index');
    // Mirror to the unified Dependency Manager progress bus
    dependencyManager._sendProgress('Hunyuan3D-2.1', 'models', 'all', {
      status,
      message,
      progress
    });
  } catch (_) {}
}
function noteModelDownloadActivity(message, progress) {
  emitModelProgress('Running', message, progress);
}
function markModelDownloadsComplete() {
  emitModelProgress('Complete', 'Hunyuan3D-2.1 models ready', 100);
}

async function isHunyuan21Running() {
  try {
    const response = await axios.get(`http://${HOST}:${PORT}/health`, { timeout: 2000 });
    return response.status === 200;
  } catch (error) {
    return false;
  }
}

function ensureDirs() {
  try { if (!fs.existsSync(SAVE_DIR)) fs.mkdirSync(SAVE_DIR, { recursive: true }); } catch {}
  try {
    const modelsDir = getHunyuan21ModelsDir();
    const cacheDir = getHunyuan21CacheDir();
    if (!fs.existsSync(modelsDir)) fs.mkdirSync(modelsDir, { recursive: true });
    if (!fs.existsSync(cacheDir)) fs.mkdirSync(cacheDir, { recursive: true });
  } catch {}
}

async function cleanupPythonProcesses() {
  log('Checking for lingering Python processes...');
  return new Promise((resolve) => {
    // First, check if there are any Python processes running
    const checkProcess = spawn('cmd.exe', ['/c', 'tasklist | findstr python.exe'], {
      stdio: 'pipe',
      windowsHide: true
    });

    let output = '';
    checkProcess.stdout.on('data', (data) => { output += data.toString(); });

    checkProcess.on('close', (code) => {
      if (output.trim() && output.includes('python.exe')) {
        log('Found Python processes, killing them...');
        log('Processes found:', output.trim());

        // Kill all Python processes
        const killProcess = spawn('cmd.exe', ['/c', 'taskkill /F /IM python.exe'], {
          stdio: 'pipe',
          windowsHide: true
        });

        killProcess.on('close', (killCode) => {
          if (killCode === 0) {
            log('Successfully killed Python processes');
          } else {
            log('Some Python processes may still be running (exit code:', killCode, ')');
          }
          setTimeout(() => resolve(), 2000);
        });
        killProcess.on('error', (err) => { log('Error killing Python processes:', err.message); resolve(); });
      } else {
        log('No Python processes found');
        resolve();
      }
    });

    checkProcess.on('error', (err) => { log('Error checking for Python processes:', err.message); resolve(); });
  });
}

/**
 * Builds the environment configuration for the Hunyuan3D-2.1 server.
 * This ensures all paths respect the user's PathManager settings.
 * @returns {Object} Environment variables for the server process
 */
function buildServerEnv() {
  const pathManager = require('./pathManager');
  
  // Start with a clean environment to avoid any conflicts
  const env = {
    // Preserve only essential system environment variables
    ...(process.platform === 'win32' ? {
      SystemRoot: process.env.SystemRoot,
      TEMP: process.env.TEMP,
      TMP: process.env.TMP,
      PATH: process.env.PATH,
      PATHEXT: process.env.PATHEXT,
      HOMEDRIVE: process.env.HOMEDRIVE,
      HOMEPATH: process.env.HOMEPATH,
      USERPROFILE: process.env.USERPROFILE,
      USERNAME: process.env.USERNAME,
      ALLUSERSPROFILE: process.env.ALLUSERSPROFILE,
      APPDATA: process.env.APPDATA,
      LOCALAPPDATA: process.env.LOCALAPPDATA,
      PROGRAMDATA: process.env.PROGRAMDATA,
      WINDIR: process.env.WINDIR,
      COMSPEC: process.env.COMSPEC,
      PROCESSOR_ARCHITECTURE: process.env.PROCESSOR_ARCHITECTURE,
      PROCESSOR_IDENTIFIER: process.env.PROCESSOR_IDENTIFIER,
      NUMBER_OF_PROCESSORS: process.env.NUMBER_OF_PROCESSORS,
      OS: process.env.OS,
      SYSTEMDRIVE: process.env.SYSTEMDRIVE
    } : {
      // Unix-like systems
      HOME: process.env.HOME,
      USER: process.env.USER,
      PATH: process.env.PATH,
      LANG: process.env.LANG || 'en_US.UTF-8',
      SHELL: process.env.SHELL,
      TMPDIR: process.env.TMPDIR || '/tmp',
      XDG_CACHE_HOME: process.env.XDG_CACHE_HOME || path.join(process.env.HOME, '.cache')
    })
  };

  try {
    // Get the cache and models paths from PathManager
    const cachePath = path.resolve(pathManager.getPipelineCachePath('Hunyuan3D-2.1'));
    const modelsPath = path.resolve(pathManager.getPipelineModelsPath('Hunyuan3D-2.1'));

    // Log the paths for debugging
    log(`[PathManager] Cache path: ${cachePath}`);
    log(`[PathManager] Models path: ${modelsPath}`);
    log(`[PathManager] HY3DGEN_MODELS will be set to: ${modelsPath}`);
    log(`[PathManager] Models will download to: ${modelsPath}/tencent/Hunyuan3D-2.1`);
    
    // Ensure directories exist with proper permissions
    const ensureDir = (dir) => {
      if (!fs.existsSync(dir)) {
        log(`Creating directory: ${dir}`);
        fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
        
        // Set proper permissions (especially important on Windows)
        try {
          if (process.platform === 'win32') {
            // On Windows, we need to ensure the directory is writable
            fs.chmodSync(dir, 0o755);
          }
        } catch (err) {
          log(`Warning: Could not set permissions for ${dir}: ${err.message}`);
        }
      }
      
      // Verify write access
      try {
        const testFile = path.join(dir, '.writetest');
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
      } catch (err) {
        throw new Error(`Cannot write to directory: ${dir} (${err.message})`);
      }
    };
    
    // Create base directories
    ensureDir(cachePath);
    ensureDir(modelsPath);
    
    // Define directory paths that need to be created
    const directoryPaths = {
      // Main cache directories
      HF_HOME: cachePath,
      HUGGINGFACE_HOME: cachePath,
      HY3DGEN_MODELS: modelsPath,

      // Specific cache subdirectories
      HUGGINGFACE_HUB_CACHE: path.join(cachePath, 'hub'),
      TRANSFORMERS_CACHE: path.join(cachePath, 'transformers'),
      HF_DATASETS_CACHE: path.join(cachePath, 'datasets'),
      TORCH_HOME: path.join(cachePath, 'torch'),
      TORCH_HUB_CACHE: path.join(cachePath, 'torch', 'hub'),
      PYTHONPYCACHEPREFIX: path.join(cachePath, 'pycache'),

      // Model specific directories
      HUNYUAN3D_MODELS: path.join(modelsPath, 'tencent', 'Hunyuan3D-2.1')
    };

    // Define configuration environment variables (not directories)
    const configVars = {
      // Python specific
      PYTHONPATH: PIPELINE_ROOT,
      PYTHONHOME: '',

      // Performance and compatibility settings
      HF_HUB_DISABLE_SYMLINKS_WARNING: '1',
      TRANSFORMERS_NO_CACHE_MIGRATION: '1',
      TOKENIZERS_PARALLELISM: 'false',

      // Python specific optimizations
      PYTHONUNBUFFERED: '1',
      PYTHONIOENCODING: 'UTF-8',

      // Disable any interactive prompts
      HF_DATASETS_OFFLINE: '1',
      HF_EVALUATE_OFFLINE: '1',

      // Disable telemetry
      HF_HUB_DISABLE_TELEMETRY: '1',
      DISABLE_TELEMETRY: '1',
      DO_NOT_TRACK: '1'
    };

    // Combine all environment variables
    const allEnvVars = { ...directoryPaths, ...configVars };

    // Apply the environment variables
    Object.assign(env, allEnvVars);

    // Create only the directories (not the config values)
    for (const [key, dir] of Object.entries(directoryPaths)) {
      if (dir && typeof dir === 'string') {
        ensureDir(dir);
      }
    }
    
    // Log important environment variables
    log('[Environment] Final environment configuration:');
    Object.entries(allEnvVars).forEach(([key, value]) => {
      log(`  ${key}=${value}`);
    });
    
    // Log system information for debugging
    log(`[System] Platform: ${process.platform}, Arch: ${process.arch}`);
    log(`[System] Node.js version: ${process.version}`);
    
  } catch (error) {
    log(`[ERROR] Failed to set up environment: ${error.message}`);
    log(error.stack);
    throw new Error(`Environment setup failed: ${error.message}`);
  }
  
  return env;
}

function resolvePythonExe() {
  // Prefer developer's portable Python to match their environment exactly
  if (fs.existsSync(PORTABLE_PYTHON)) return PORTABLE_PYTHON;
  if (fs.existsSync(VENV_PYTHON_ROOT)) return VENV_PYTHON_ROOT;
  if (fs.existsSync(VENV_PYTHON_SCRIPTS)) return VENV_PYTHON_SCRIPTS;
  return 'python';
}

function resolvePipExe() {
  // Use our existing venv pip
  if (fs.existsSync(VENV_PIP_SCRIPTS)) return VENV_PIP_SCRIPTS;
  if (fs.existsSync(VENV_PIP_ROOT)) return VENV_PIP_ROOT;
  return 'pip';
}

async function installPyTorch(pipExe, env) {
  return new Promise((resolve, reject) => {
    log('Installing PyTorch with CUDA support...');
    emitModelProgress('Running', 'Installing PyTorch with CUDA...', 15);

    // Install PyTorch with CUDA 12.1 support (compatible with CUDA 12.6)
    const torchArgs = [
      'install', 'torch', 'torchvision', 'torchaudio',
      '--index-url', 'https://download.pytorch.org/whl/cu121',
      '--no-cache-dir'
    ];

    log('Installing PyTorch:', pipExe, torchArgs.join(' '));

    const torchChild = spawn(pipExe, torchArgs, {
      cwd: PIPELINE_ROOT,
      env: env,
      windowsHide: true
    });

    torchChild.stdout.on('data', (data) => {
      const text = data.toString().trim();
      if (text) {
        log(`[torch install] ${text}`);
        if (/Installing collected packages|Successfully installed/i.test(text)) {
          emitModelProgress('Running', 'Installing PyTorch...', 25);
        }
      }
    });

    torchChild.stderr.on('data', (data) => {
      const text = data.toString().trim();
      if (text) log(`[torch install][stderr] ${text}`);
    });

    torchChild.on('close', (code) => {
      if (code === 0) {
        log('PyTorch installed successfully');
        resolve();
      } else {
        reject(new Error(`PyTorch installation failed with code ${code}`));
      }
    });

    torchChild.on('error', (err) => {
      reject(new Error(`PyTorch installation error: ${err.message}`));
    });
  });
}

async function installCustomModules(pipExe, env) {
  return new Promise((resolve, reject) => {
    log('Installing custom compiled modules...');
    emitModelProgress('Running', 'Compiling custom modules...', 40);

    const customModules = [
      path.join(PIPELINE_ROOT, 'hy3dpaint', 'custom_rasterizer'),
      path.join(PIPELINE_ROOT, 'hy3dpaint', 'DifferentiableRenderer')
    ];

    let completed = 0;
    const total = customModules.length;

    const installNext = () => {
      if (completed >= total) {
        log('All custom modules installed successfully');
        return resolve();
      }

      const modulePath = customModules[completed];
      if (!fs.existsSync(modulePath)) {
        log(`Custom module not found: ${modulePath}, skipping`);
        completed++;
        return installNext();
      }

      log(`Installing custom module: ${modulePath}`);
      const moduleArgs = ['install', '-e', modulePath, '--no-cache-dir', '--no-build-isolation'];

      const moduleChild = spawn(pipExe, moduleArgs, {
        cwd: PIPELINE_ROOT,
        env: env,
        windowsHide: true
      });

      moduleChild.stdout.on('data', (data) => {
        const text = data.toString().trim();
        if (text) log(`[custom module] ${text}`);
      });

      moduleChild.stderr.on('data', (data) => {
        const text = data.toString().trim();
        if (text) log(`[custom module][stderr] ${text}`);
      });

      moduleChild.on('close', (code) => {
        completed++;
        if (code === 0) {
          log(`Custom module installed: ${path.basename(modulePath)}`);
        } else {
          log(`Custom module failed: ${path.basename(modulePath)} (code ${code})`);
        }
        installNext();
      });

      moduleChild.on('error', (err) => {
        log(`Custom module error: ${err.message}`);
        completed++;
        installNext();
      });
    };

    installNext();
  });
}

/**
 * Ensures all required dependencies are installed and available.
 * @param {Function} [progressCb=null] - Optional progress callback function
 * @returns {Promise<void>}
 * @throws {Error} If any critical dependency is missing or cannot be verified
 */
async function ensureDependencies(progressCb = null) {
  const logPrefix = '[Dependency Check]';
  log(`${logPrefix} Starting dependency verification...`);
  
  try {
    // Set up environment with proper paths
    const env = buildServerEnv();
    
    // Check if Python exists
    const pythonExe = resolvePythonExe();
    log(`${logPrefix} Checking Python executable at: ${pythonExe}`);
    
    if (!fs.existsSync(pythonExe)) {
      throw new Error(`Python executable not found at: ${pythonExe}. Please ensure Python is properly installed.`);
    }
    
    // Check Python version
    try {
      const pythonVersion = await new Promise((resolve, reject) => {
        const proc = spawn(pythonExe, ['--version'], { windowsHide: true });
        let output = '';
        
        proc.stdout.on('data', (data) => output += data.toString());
        proc.stderr.on('data', (data) => output += data.toString());
        
        proc.on('close', (code) => {
          if (code === 0) {
            resolve(output.trim());
          } else {
            reject(new Error(`Failed to get Python version (code ${code}): ${output}`));
          }
        });
        
        proc.on('error', (err) => reject(err));
      });
      
      log(`${logPrefix} ${pythonVersion}`);
    } catch (err) {
      log(`${logPrefix} Warning: Could not verify Python version: ${err.message}`);
    }
    
    // Check if API server exists
    log(`${logPrefix} Verifying API server at: ${API_SERVER_PATH}`);
    if (!fs.existsSync(API_SERVER_PATH)) {
      throw new Error(`API server not found at: ${API_SERVER_PATH}. The application may be corrupted.`);
    }
    
    // If using developer's portable environment, skip pip checks
    if (pythonExe === PORTABLE_PYTHON) {
      log(`${logPrefix} Using developer portable environment; skipping pip checks`);
      emitModelProgress('Complete', 'Dependencies ready (Portable Mode)', 50);
      return;
    }
    
    // Check for pip and required packages
    const pipExe = resolvePipExe();
    log(`${logPrefix} Checking pip executable at: ${pipExe || 'Not found'}`);
    
    if (!pipExe) {
      throw new Error('pip executable not found. Please ensure pip is installed with your Python installation.');
    }
    
    // Check pip version
    try {
      const pipVersion = await new Promise((resolve, reject) => {
        const proc = spawn(pipExe, ['--version'], { windowsHide: true });
        let output = '';
        
        proc.stdout.on('data', (data) => output += data.toString());
        proc.stderr.on('data', (data) => output += data.toString());
        
        proc.on('close', (code) => {
          if (code === 0) {
            resolve(output.trim());
          } else {
            reject(new Error(`Failed to get pip version (code ${code}): ${output}`));
          }
        });
        
        proc.on('error', (err) => reject(err));
      });
      
      log(`${logPrefix} ${pipVersion}`);
    } catch (err) {
      log(`${logPrefix} Warning: Could not verify pip version: ${err.message}`);
    }
    
    // Check for required packages
    log(`${logPrefix} Checking for required Python packages...`);
    emitModelProgress('Running', 'Checking Python packages...', 30);
    
    // List of required packages to check
    const requiredPackages = [
      'uvicorn',
      'fastapi',
      'torch',
      'transformers',
      'diffusers',
      'huggingface-hub',
      'numpy',
      'pillow',
      'tqdm',
      'omegaconf',
      'einops',
      'safetensors'
    ];
    
    // Check each package
    for (const pkg of requiredPackages) {
      const isInstalled = await new Promise((resolve) => {
        const checkChild = spawn(pythonExe, ['-c', `import ${pkg}`], { 
          cwd: PIPELINE_ROOT, 
          env, 
          windowsHide: true 
        });
        
        checkChild.on('close', (code) => {
          resolve(code === 0);
        });
        
        checkChild.on('error', () => resolve(false));
      });
      
      if (!isInstalled) {
        log(`${logPrefix} Package not found: ${pkg}`);
        throw new Error(`Required package '${pkg}' is not installed. Please install all dependencies.`);
      }
      
      log(`${logPrefix} Verified package: ${pkg}`);
    }
    
    log(`${logPrefix} All dependencies verified successfully`);
    emitModelProgress('Complete', 'Dependencies ready', 50);
    
  } catch (error) {
    const errorMsg = `Dependency check failed: ${error.message}`;
    log(`${logPrefix} ${errorMsg}`);
    emitModelProgress('Error', errorMsg, 0);
    
    // Provide more helpful error messages for common issues
    if (error.message.includes('Python executable not found')) {
      throw new Error(`${error.message}\n\nPlease ensure Python 3.8+ is installed and added to your system PATH.`);
    } else if (error.message.includes('pip executable not found')) {
      throw new Error(`${error.message}\n\nPlease ensure pip is installed with your Python installation.`);
    } else if (error.message.includes('Required package')) {
      throw new Error(`${error.message}\n\nPlease install the missing package using: pip install ${error.message.split("'")[1]}`);
    }
    
    throw error;
  }
}

function startServer(progressCb = null) {
  return new Promise(async (resolve, reject) => {
    let waitLoopTimeout;
    let child;
    
    const cleanup = () => {
      if (waitLoopTimeout) clearTimeout(waitLoopTimeout);
      if (child) {
        // Remove all listeners to prevent memory leaks
        child.stdout?.removeAllListeners('data');
        child.stderr?.removeAllListeners('data');
        child.removeAllListeners('close');
        child.removeAllListeners('error');
      }
    };
    
    let env;
    
    try {
      log('Starting Hunyuan3D-2.1 server...');
      
      // Ensure all directories exist first
      ensureDirs();
      
      // Get the environment with all necessary paths set
      env = buildServerEnv();
      
      // Log all environment variables for debugging
      log('Full environment variables:');
      Object.entries(env).forEach(([key, value]) => {
        // Don't log sensitive information
        if (!key.toLowerCase().includes('token') && !key.toLowerCase().includes('key')) {
          log(`  ${key}=${value}`);
        } else {
          log(`  ${key}=[REDACTED]`);
        }
      });
      
      // Force set the environment variables for the current process
      Object.assign(process.env, env);
      
      const pathManager = require('./pathManager');
      const modelsDir = pathManager.getPipelineModelsPath('Hunyuan3D-2.1');
      log(`[CONFIG] Using models directory from settings: ${modelsDir}`);
      env.HY3DGEN_MODELS = modelsDir;
      
      // Get Python executable
      const pythonExe = resolvePythonExe();
      if (!pythonExe) {
        throw new Error('Python executable not found');
      }
      
      // Check if server is already running
      if (await isHunyuan21Running()) {
        log('Hunyuan3D-2.1 server is already running');
        markModelDownloadsComplete();
        cleanup();
        return resolve();
      }
      
      // Ensure dependencies are installed
      try {
        await ensureDependencies(progressCb);
      } catch (err) {
        cleanup();
        throw new Error(`Dependency check failed: ${err.message}`);
      }
      
      // Log important environment variables for debugging
      const logEnvVars = [
        'HF_HOME', 'HUGGINGFACE_HUB_CACHE', 'TRANSFORMERS_CACHE',
        'HF_DATASETS_CACHE', 'HY3DGEN_MODELS', 'PYTHONPATH', 'TORCH_HOME',
        'HUGGINGFACE_HUB_CACHE', 'HF_HUB_CACHE', 'HUGGINGFACE_HOME'
      ];
      
      // Log important environment variables for debugging
      log('Environment variables:');
      logEnvVars.forEach(varName => {
        log(`  ${varName}=${env[varName] || 'not set'}`);
      });
      
      // Disable transformers cache migration to prevent errors
      env.TRANSFORMERS_NO_CACHE_MIGRATION = '1';
      
      try {
        // Check if transformers cache migration is needed
        const cacheMigrationNeeded = await new Promise((resolve, reject) => {
          const proc = spawn(pythonExe, ['-c', `import transformers; print(transformers.__version__)`], { 
            cwd: PIPELINE_ROOT, 
            env, 
            windowsHide: true 
          });
          
          let output = '';
          
          proc.stdout.on('data', (data) => output += data.toString());
          proc.stderr.on('data', (data) => output += data.toString());
          
          proc.on('close', (code) => {
            if (code === 0) {
              resolve(output.trim() !== '4.12.0');
            } else {
              reject(new Error(`Failed to check transformers version (code ${code}): ${output}`));
            }
          });
          
          proc.on('error', (err) => reject(err));
        });
        
        if (cacheMigrationNeeded) {
          log('Transformers cache migration needed');
          const migrationProc = spawn(pythonExe, [
            '-c',
            `import transformers; 
             if hasattr(transformers, "_migration") and hasattr(transformers._migration, "migrate"): 
               transformers._migration.migrate()`,
          ], { 
            cwd: PIPELINE_ROOT, 
            env: { ...env, TRANSFORMERS_NO_CACHE_MIGRATION: '1' },
            windowsHide: true,
            stdio: 'inherit'
          });
          
          await new Promise((resolve) => {
            migrationProc.on('close', (code) => {
              if (code === 0) {
                log('Transformers cache migration complete');
              } else {
                log(`Transformers cache migration failed (code ${code})`);
              }
              resolve();
            });
          });
        }
      } catch (err) {
        log('Error during transformers cache migration:', err.message);
      }
      
      // Debug: Check where the model will be downloaded
      log(`Model will be downloaded to: ${env.HF_HOME || env.HY3DGEN_MODELS || 'default location'}`);
      
      // Build command line arguments
      // IMPORTANT: model_path should be the HuggingFace repo ID, not an absolute path
      // The Python code will use HY3DGEN_MODELS environment variable to determine where to download/load models
      const args = [
        API_SERVER_PATH,
        '--host', HOST,
        '--port', String(PORT),
        '--model_path', 'tencent/Hunyuan3D-2.1',  // HuggingFace repo ID
        '--subfolder', 'hunyuan3d-dit-v2-1',
        '--cache-path', env.HF_HOME,  // Use HF_HOME as the cache path
        '--low_vram_mode'
      ];
      
      // Log the environment for debugging
      log('Environment variables for Python process:');
      Object.entries(env).forEach(([key, value]) => {
        log(`  ${key}=${value}`);
      });
      
      // Create a clean environment with only the variables we want to pass
      const pythonEnv = {
        // Preserve critical system environment variables
        ...process.env,
        // Override with our custom environment
        ...env,
        // Ensure these critical paths are set correctly
        PATH: process.env.PATH,  // Preserve system PATH
        SystemRoot: process.env.SystemRoot,  // Required on Windows
        // Disable any cache migration prompts
        TRANSFORMERS_NO_CACHE_MIGRATION: '1',
        // Python specific settings
        PYTHONUNBUFFERED: '1',
        PYTHONIOENCODING: 'UTF-8',
        // Disable any interactive prompts
        HF_DATASETS_OFFLINE: '1',
        HF_EVALUATE_OFFLINE: '1',
        TOKENIZERS_PARALLELISM: 'false'
      };
      
      // Log the final environment for debugging
      log('Final Python process environment:');
      [
        'HF_HOME', 'HUGGINGFACE_HUB_CACHE', 'TRANSFORMERS_CACHE',
        'HF_DATASETS_CACHE', 'HY3DGEN_MODELS', 'PYTHONPATH', 'TORCH_HOME',
        'HUGGINGFACE_HUB_CACHE', 'HF_HUB_CACHE', 'HUGGINGFACE_HOME'
      ].forEach(key => {
        log(`  ${key}=${pythonEnv[key] || 'not set'}`);
      });
      
      // Make sure the cache directories exist
      const requiredDirs = [
        env.HF_HOME,
        env.HUGGINGFACE_HUB_CACHE,
        env.TRANSFORMERS_CACHE,
        env.HF_DATASETS_CACHE,
        env.HY3DGEN_MODELS,
        path.join(env.HY3DGEN_MODELS, 'tencent'),
        path.join(env.HY3DGEN_MODELS, 'tencent/Hunyuan3D-2.1'),
        path.join(env.HY3DGEN_MODELS, 'hy3dpaint')  // Add hy3dpaint directory
      ];
      
      requiredDirs.forEach(dir => {
        if (dir) {
          try {
            if (!fs.existsSync(dir)) {
              log(`Creating directory: ${dir}`);
              fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
            }
            // Verify write access
            const testFile = path.join(dir, '.writetest');
            fs.writeFileSync(testFile, 'test');
            fs.unlinkSync(testFile);
          } catch (err) {
            log(`Warning: Could not create or write to directory ${dir}: ${err.message}`);
          }
        }
      });
      
      log(`Starting API server with Python: ${pythonExe}`);
      log(`Working directory: ${PORTABLE_ROOT}`);
      log(`API server path: ${API_SERVER_PATH}`);
      log(`Command: ${pythonExe} ${args.join(' ')}`);
      
      // Ensure the target models directory exists and is writable
      const hfModelsDir = env.HY3DGEN_MODELS;
      const targetModelDir = path.join(hfModelsDir, 'tencent', 'Hunyuan3D-2.1');
      
      try {
        // Create all required directories with proper permissions
        [hfModelsDir, path.join(hfModelsDir, 'tencent'), targetModelDir].forEach(dir => {
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
            log(`Created directory: ${dir}`);
          }
          // Verify write access
          const testFile = path.join(dir, '.writetest');
          fs.writeFileSync(testFile, 'test');
          fs.unlinkSync(testFile);
        });
      } catch (err) {
        throw new Error(`Cannot write to model directory ${targetModelDir}: ${err.message}`);
      }
      
      log(`Starting Python process with environment:`);
      log(`- Working directory: ${PORTABLE_ROOT}`);
      log(`- Python executable: ${pythonExe}`);
      log(`- Model directory: ${targetModelDir}`);
      log(`- Cache directory: ${hfModelsDir}`);
      
      // Start the Python process with the environment
      child = spawn(pythonExe, args, {
        cwd: PORTABLE_ROOT,
        env: pythonEnv,
        stdio: ['ignore', 'pipe', 'pipe'],
        windowsHide: true,
        shell: false,  // Don't use shell to avoid issues with environment on Windows
        detached: false,
        windowsVerbatimArguments: false
      });
      
      // Log process start
      log(`Started Python process (PID: ${child.pid}) with args: ${args.join(' ')}`);

      global.hunyuan21Process = child;

      // Track last emitted percents to reduce log spam
      let lastPerc = { diffusion: -1, flash: -1, download: -1 };

      // Track last activity across server output and health checks
      let lastActivity = Date.now();
      let serverReady = false;

      const onLine = (line) => {
        const text = line.toString().trim();
        if (!text) return;
        // Any server output counts as activity (downloads, init, logs)
        lastActivity = Date.now();

        // Filter out noisy status GET logs
        if (/GET\s+\/status\//i.test(text)) return;

        // Parse generation progress (Diffusion Sampling)
        const diffMatch = text.match(/Diffusion Sampling::\s+(\d+)%/i);
        if (diffMatch) {
          const p = parseInt(diffMatch[1], 10);
          if (p !== lastPerc.diffusion) {
            lastPerc.diffusion = p;
            log(`[progress] Diffusion Sampling ${p}%`);
            emitModelProgress('Running', `Diffusion Sampling ${p}%`, p);
          }
          return;
        }

        // Parse generation progress (FlashVDM Decoding)
        const flashMatch = text.match(/FlashVDM.*Decoding:\s+(\d+)%/i);
        if (flashMatch) {
          const p = parseInt(flashMatch[1], 10);
          if (p !== lastPerc.flash) {
            lastPerc.flash = p;
            log(`[progress] FlashVDM Decoding ${p}%`);
            emitModelProgress('Running', `FlashVDM Decoding ${p}%`, p);
          }
          return;
        }

        // Mirror Hugging Face downloads (only on percent changes)
        const fetchMatch = text.match(/Fetching\s+\d+\s+files:\s+(\d+)%/i);
        const pctMatch = text.match(/(\d+)%\|/); // e.g., " 47%|████..."
        if (fetchMatch) {
          const pct = Math.max(10, Math.min(98, parseInt(fetchMatch[1], 10)));
          if (pct !== lastPerc.download) {
            lastPerc.download = pct;
            noteModelDownloadActivity('Downloading Hunyuan3D-2.1 models...', pct);
          }
        } else if (pctMatch) {
          const pct = Math.max(10, Math.min(98, parseInt(pctMatch[1], 10)));
          if (pct !== lastPerc.download) {
            lastPerc.download = pct;
            noteModelDownloadActivity('Downloading model weights...', pct);
          }
        } else if (/snapshot_download|Downloading/i.test(text)) {
          noteModelDownloadActivity(text, lastPerc.download > 0 ? lastPerc.download : 20);
        } else if (/Uvicorn running on/i.test(text) || /Application startup complete/i.test(text) || /INFO.*Started server process/i.test(text)) {
          markModelDownloadsComplete();
          serverReady = true;
        } else {
          // Suppress other server logs to reduce noise
          // log(`[server] ${text}`);
        }
      };

      const onStdout = (d) => { 
        onLine(d); 
        try { 
          logger.info(`[Hunyuan3D-2.1][stdout] ${d.toString().trim()}`); 
        } catch(_) {} 
      };
      
      const onStderr = (d) => { 
        onLine(d); 
        try { 
          logger.info(`[Hunyuan3D-2.1][stderr] ${d.toString().trim()}`); 
        } catch(_) {}
      };
      
      const onClose = (code) => {
        const exitCode = code !== null ? code : 'unknown';
        log(`Server process exited with code ${exitCode}`);
        if (!serverReady) {
          cleanup();
          reject(new Error(`Server process exited with code ${exitCode}`));
        }
      };
      
      const onError = (err) => {
        log('Server process error:', err);
        cleanup();
        reject(err);
      };

      child.stdout.on('data', onStdout);
      child.stderr.on('data', onStderr);
      child.on('close', onClose);
      child.on('error', onError);

      // Wait for API server to respond
      const start = Date.now();
      const ABS_TIMEOUT_MS = 45 * 60 * 1000;       // absolute cap (45 min) for very first run
      const INACTIVITY_TIMEOUT_MS = 12 * 60 * 1000; // if no activity for 12 min, assume hang
      
      const waitLoop = async () => {
        try {
          const ok = await axios.get(`http://${HOST}:${PORT}/health`, { 
            timeout: 2000 
          }).then(() => true).catch(() => false);
          
          if (ok) {
            log('Hunyuan3D-2.1 API server is ready');
            cleanup();
            resolve();
            return;
          }
        } catch (err) {
          log('Health check error:', err.message);
        }
        
        const now = Date.now();
        const inactiveTooLong = now - lastActivity > INACTIVITY_TIMEOUT_MS;
        const exceededAbsolute = now - start > ABS_TIMEOUT_MS;
        
        if (inactiveTooLong && exceededAbsolute) {
          const error = new Error('Timeout waiting for Hunyuan3D-2.1 API server');
          log(error.message);
          cleanup();
          reject(error);
          return;
        }
        
        waitLoopTimeout = setTimeout(waitLoop, 2000);
      };
      
      waitLoop();
      
    } catch (error) {
      cleanup();
      log('Error in startServer:', error);
      reject(error);
    }
  });
}

async function ensureServer(progressCb = null) {
  if (await isHunyuan21Running()) { log('Server already running'); return; }
  await cleanupPythonProcesses();
  await startServer(progressCb);
}

function encodeImageToBase64(filePath) {
  const buf = fs.readFileSync(filePath);
  return buf.toString('base64');
}

// Helper function to send progress events to frontend
function sendProgressToFrontend(sessionId, stage, progress, message) {
  try {
    const { BrowserWindow } = require('electron');
    const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
    if (mainWindow) {
      mainWindow.webContents.send('pipeline-status', {
        session_id: sessionId,
        stage: stage,
        progress: progress,
        message: message,
        timestamp: Date.now()
      });
    }
  } catch (error) {
    // Silently ignore IPC errors
  }
}

// Helper function to generate session ID from image path
function generateSessionId(imagePath) {
  const path = require('path');
  const crypto = require('crypto');
  const basename = path.basename(imagePath, path.extname(imagePath));
  return crypto.createHash('md5').update(basename + Date.now()).digest('hex').substring(0, 8);
}

async function generate3DModel(imagePath, outputPath, settings = {}, progressCb = null) {
  log('generate3DModel', { imagePath, outputPath, settings });

  // Generate session ID for progress tracking
  const sessionId = generateSessionId(imagePath);

  // Helper function to send progress updates
  const sendProgress = (stage, progress, message) => {
    if (progressCb) {
      progressCb({ stage, progress, message, session_id: sessionId });
    }
    sendProgressToFrontend(sessionId, stage, progress, message);
  };

  // 1) Ensure server is running
  log('Ensuring API server...');
  sendProgress('hunyuan_preprocessing', 5, 'Starting Hunyuan3D-2.1 server...');
  await ensureServer(progressCb);
  log('Hunyuan3D-2.1 API server ready');

  // 2) Encode image to base64 (raw base64 string, not data URI)
  sendProgress('hunyuan_preprocessing', 10, 'Processing input image...');
  const imageBase64 = encodeImageToBase64(imagePath);

  // 3) Prepare minimal request that strictly matches API schema (avoid 422)
  // Map UI preset to developer-aligned parameters (clamped to API limits)
  const preset = (settings.preset || 'medium').toLowerCase();
  const presets = {
    fast:   { num_inference_steps: 5,  guidance_scale: 5.0, octree_resolution: 256, num_chunks: 8000, face_count: 40000 },
    medium: { num_inference_steps: 10, guidance_scale: 5.0, octree_resolution: 256, num_chunks: 8000, face_count: 40000 },
    high:   { num_inference_steps: 20, guidance_scale: 5.0, octree_resolution: 256, num_chunks: 8000, face_count: 40000 }
  };
  const chosen = presets[preset] || presets.medium;
  const requestData = {
    image: imageBase64,
    remove_background: settings.remove_background !== false, // default true
    texture: settings.texture !== false, // default true
    seed: Number.isInteger(settings.seed) ? settings.seed : 1234,
    octree_resolution: Math.max(64, Math.min(512, settings.octree_resolution ?? chosen.octree_resolution)),
    num_inference_steps: Math.max(1, Math.min(20, settings.num_inference_steps ?? chosen.num_inference_steps)),
    guidance_scale: Math.max(0.1, Math.min(20.0, settings.guidance_scale ?? chosen.guidance_scale)),
    num_chunks: Math.max(1000, Math.min(20000, settings.num_chunks ?? chosen.num_chunks)),
    face_count: Math.max(1000, Math.min(100000, settings.face_count ?? chosen.face_count))
  };

  log('Sending generation request. texture=', requestData.texture);
  sendProgress('hunyuan_preprocessing', 15, 'Submitting generation request...');

  try {
    const base = `http://${HOST}:${PORT}`;

    // 4) Start async generation task
    const sendResp = await axios.post(`${base}/send`, requestData, {
      timeout: 0, // no timeout
      headers: { 'Content-Type': 'application/json' },
      maxBodyLength: Infinity,
      maxContentLength: Infinity
    });

    const uid = sendResp?.data?.uid;
    if (!uid) throw new Error('No uid returned from /send');
    log('Task queued:', uid);
    sendProgress('hunyuan_diffusion', 20, 'Generation task queued, starting diffusion...');

    // 5) Poll for status until completed with progress tracking
    let lastStatus = null;
    let pollCount = 0;
    const startTime = Date.now();

    while (true) {
      // Backoff/pause between polls
      await new Promise(r => setTimeout(r, 5000));
      pollCount++;

      let statusData;
      try {
        const statusResp = await axios.get(`${base}/status/${uid}`, { timeout: 0 });
        statusData = statusResp?.data || {};
      } catch (pollErr) {
        log('Status poll error:', pollErr.message);
        continue;
      }

      const st = statusData.status;
      if (!st) { continue; }

      // Calculate elapsed time for progress estimation
      const elapsedTime = Date.now() - startTime;
      const elapsedMinutes = elapsedTime / (1000 * 60);

      if (st === 'processing') {
        if (st !== lastStatus) {
          lastStatus = st;
          log(`[status] ${st}`);
          sendProgress('hunyuan_diffusion', 25, 'Generating 3D structure...');
        }

        // Estimate progress during processing (25% to 70% over ~2-5 minutes)
        const processingProgress = Math.min(70, 25 + (elapsedMinutes / 5) * 45);
        sendProgress('hunyuan_diffusion', Math.round(processingProgress), 'Diffusion sampling in progress...');
        continue;
      }

      if (st === 'texturing') {
        if (st !== lastStatus) {
          lastStatus = st;
          log(`[status] ${st}`);
          sendProgress('hunyuan_texture', 75, 'Generating textures...');
        }

        // Estimate progress during texturing (75% to 90% over ~1-2 minutes)
        const texturingProgress = Math.min(90, 75 + (elapsedMinutes / 2) * 15);
        sendProgress('hunyuan_texture', Math.round(texturingProgress), 'Applying PBR textures...');
        continue;
      }

      if (st === 'completed' && statusData.model_base64) {
        // Add face reduction stage before completion
        sendProgress('hunyuan_decoding', 92, 'Optimizing mesh geometry...');

        // Check if face_count is specified and different from default
        const targetFaceCount = requestData.face_count;
        if (targetFaceCount && targetFaceCount !== 40000) {
          sendProgress('hunyuan_decoding', 95, `Reducing mesh to ${targetFaceCount.toLocaleString()} faces...`);
          log(`Face reduction: targeting ${targetFaceCount} faces`);
        } else {
          sendProgress('hunyuan_decoding', 95, 'Applying mesh optimization...');
        }

        sendProgress('hunyuan_decoding', 98, 'Converting to GLB format...');
        const buf = Buffer.from(statusData.model_base64, 'base64');
        fs.writeFileSync(outputPath, buf);
        log('3D model generated successfully:', outputPath);
        sendProgress('hunyuan_decoding', 100, 'Generation completed successfully!');
        return { success: true, outputPath, message: 'Hunyuan3D-2.1 generation completed' };
      }

      if (st === 'error') {
        throw new Error(statusData.message || 'Generation failed');
      }

      // Unknown status; keep polling but update progress
      if (st !== lastStatus) {
        lastStatus = st;
        log(`[status] ${st}`);
        const unknownProgress = Math.min(90, 30 + (elapsedMinutes / 3) * 60);
        sendProgress('hunyuan_diffusion', Math.round(unknownProgress), `Processing: ${st}...`);
      }
    }
  } catch (error) {
    const details = error?.response?.data ? (typeof error.response.data === 'string' ? error.response.data : JSON.stringify(error.response.data)) : '';
    log('Generation failed:', error.message, details);
    sendProgress('hunyuan_diffusion', 0, `Generation failed: ${error.message}`);
    throw new Error(`Hunyuan3D-2.1 generation failed: ${error.message}${details ? ' | ' + details : ''}`);
  }
}

module.exports = {
  generate3DModel,
  cleanupPythonProcesses,
  isHunyuan21Running: isHunyuan21Running
};

