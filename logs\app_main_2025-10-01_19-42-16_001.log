========================================
AIStudio Real-time Log: main
Started: 2025-10-02T00:42:16.545Z
File: app_main_2025-10-01_19-42-16_001.log
========================================

[2025-10-02T00:42:16.811Z] [INFO] AIStudio application started successfully
[2025-10-02T00:42:16.811Z] [INFO] [main] AIStudio application started successfully
[2025-10-02T00:42:16.854Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-10-02T00:42:17.738Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-10-02T00:42:17.921Z] [ERROR] Unhandled Rejection at: [object Promise], reason: Error: Attempted to register a second handler for 'get-folder-size'
[2025-10-02T00:42:17.921Z] [ERROR] [main] ERROR: Unhandled Rejection at: [object Promise], reason: Error: Attempted to register a second handler for 'get-folder-size'
[2025-10-02T00:42:17.921Z] [ERROR] Unhandled Rejection: Error: Attempted to register a second handler for 'get-folder-size'
[2025-10-02T00:42:35.294Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-10-01_19-42-35_001.log
[2025-10-02T00:42:35.294Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-10-02T00:42:38.364Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-10-02T00:42:38.365Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-10-02T00:42:39.377Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-10-02T00:43:21.286Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_python installation_2025-10-01_19-43-21_001.log
[2025-10-02T00:43:21.287Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting python installation for ImageGeneration
[2025-10-02T00:43:21.288Z] [INFO] [dependency_imagegeneration] DependencyManager: Installing dependencies for ImageGeneration (python:all)
[2025-10-02T00:43:21.288Z] [INFO] [dependency_imagegeneration] DependencyManager: Component type: string, Component value: 'python'
[2025-10-02T00:43:21.289Z] [INFO] [dependency_imagegeneration] DependencyManager: Name type: string, Name value: 'all'
[2025-10-02T00:43:21.289Z] [INFO] [dependency_imagegeneration] DependencyManager: About to check routing for ImageGeneration with component python
[2025-10-02T00:43:21.290Z] [INFO] [DependencyManager] Sending initial progress for ImageGeneration python all
[2025-10-02T00:43:21.291Z] [INFO] [RealtimeLogger] Started logging dependency_imagegeneration to: dependency_bundled installation_2025-10-01_19-43-21_001.log
[2025-10-02T00:43:21.291Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting bundled installation for ImageGeneration
[2025-10-02T00:43:21.291Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting portable installation of ImageGeneration module
[2025-10-02T00:43:21.292Z] [INFO] [dependency_imagegeneration] DependencyManager: Source: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-10-02T00:43:21.292Z] [INFO] [dependency_imagegeneration] DependencyManager: Extraction Target: N:\AIStudio\pipelines\ImageGeneration
[2025-10-02T00:43:21.292Z] [INFO] [dependency_imagegeneration] DependencyManager: Checking for existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-10-02T00:43:21.293Z] [INFO] [dependency_imagegeneration] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\ImageGeneration
[2025-10-02T00:43:21.328Z] [INFO] [dependency_imagegeneration] DependencyManager: Existing directory removed successfully
[2025-10-02T00:43:21.330Z] [INFO] [dependency_imagegeneration] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip
[2025-10-02T00:43:21.331Z] [INFO] [dependency_imagegeneration] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\ImageGeneration\dependencies\ImageGeneration.zip to N:\AIStudio\pipelines
