"""
Download script for ImageGeneration models
Downloads SDXL Base and Refiner models

This script uses the central model configuration from model_config.py
"""

import os
import sys
from pathlib import Path
import argparse

# Add parent directory to path to import model_config
sys.path.append(str(Path(__file__).parent.parent))
from model_config import setup_pipeline_environment

def check_model_exists(local_path, model_name):
    """Check if a model is already downloaded and complete"""
    if not local_path.exists():
        return False

    # Check for essential files that indicate a complete download
    essential_files = ["model_index.json"]

    for file in essential_files:
        if not (local_path / file).exists():
            print(f"Model {model_name} incomplete: missing {file}")
            return False

    # Check for essential directories
    essential_dirs = ["scheduler", "tokenizer"]
    for dir_name in essential_dirs:
        if not (local_path / dir_name).exists():
            print(f"Model {model_name} incomplete: missing {dir_name} directory")
            return False

    print(f"Model {model_name} already exists and appears complete")
    return True

def download_model(repo_id, local_path, model_name):
    """Download a single model using hf_xet for improved performance"""
    try:
        # Check if model already exists and is complete
        if check_model_exists(local_path, model_name):
            print(f"SKIPPED: {model_name} already downloaded and complete")
            return True

        # Note: hf_xet is available but requires complex setup
        # For now, using optimized huggingface_hub approach with portable cache
        print(f"Note: hf_xet is available but using huggingface_hub for reliable downloads")

        # Use optimized huggingface_hub with portable cache
        from huggingface_hub import snapshot_download

        print(f"Downloading {model_name} from {repo_id} using huggingface_hub...")
        print(f"Target path: {local_path}")

        # Create directory if it doesn't exist
        local_path.mkdir(parents=True, exist_ok=True)

        # Download the model with optimized settings
        snapshot_download(
            repo_id=repo_id,
            local_dir=str(local_path),
            local_dir_use_symlinks=False,
            resume_download=True,
            max_workers=4,  # Parallel downloads
            ignore_patterns=["*.msgpack", "*.h5", "*.ot"],  # Skip unnecessary files
        )

        print(f"SUCCESS: {model_name} downloaded successfully using huggingface_hub!")
        return True

    except ImportError:
        print("ERROR: huggingface_hub not available. Please install it first:")
        return False
    except Exception as e:
        print(f"ERROR: {model_name} download failed: {e}")
        return False

def download_all_models(models_dir=None):
    """Download all ImageGeneration models using the central configuration"""
    # Get the config
    config = setup_pipeline_environment("image_generation")
    
    # If models_dir is provided, use it (for backward compatibility)
    if models_dir is None:
        models_dir = config.cache_dir
    else:
        models_dir = Path(models_dir)
        
    models_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"Using models directory: {models_dir}")
    
    # SDXL Base 1.0
    sdxl_base_path = models_dir / "stabilityai/stable-diffusion-xl-base-1.0"
    download_model(
        "stabilityai/stable-diffusion-xl-base-1.0",
        sdxl_base_path,
        "SDXL Base 1.0"
    )
    
    # SDXL Refiner 1.0
    sdxl_refiner_path = models_dir / "stabilityai/stable-diffusion-xl-refiner-1.0"
    download_model(
        "stabilityai/stable-diffusion-xl-refiner-1.0",
        sdxl_refiner_path,
        "SDXL Refiner 1.0"
    )
    
    # SDXL Turbo
    sdxl_turbo_path = models_dir / "stabilityai/sdxl-turbo"
    download_model(
        "stabilityai/sdxl-turbo",
        sdxl_turbo_path,
        "SDXL Turbo"
    )
    
    # Stable Diffusion 1.5
    sd1_5_path = models_dir / "runwayml/stable-diffusion-v1-5"
    download_model(
        "runwayml/stable-diffusion-v1-5",
        sd1_5_path,
        "Stable Diffusion 1.5"
    )
    
    success_count = 4
    total_count = 4
    
    print(f"Download complete: {success_count}/{total_count} models downloaded successfully")
    
    if success_count == total_count:
        print("SUCCESS: All models downloaded successfully!")
        return True
    else:
        print(f"WARNING: {total_count - success_count} models failed to download")
        return False

def setup_portable_cache(models_dir=None):
    """Set up environment variables using the central configuration"""
    # Use the central config to set up the environment
    config = setup_pipeline_environment("image_generation")
    
    # If a specific models_dir was provided, use it (for backward compatibility)
    if models_dir is not None:
        models_dir = Path(models_dir)
        models_dir.mkdir(parents=True, exist_ok=True)
        return str(models_dir)
        
    return config.cache_dir

def main():
    parser = argparse.ArgumentParser(description="Download ImageGeneration models")
    parser.add_argument("--models-dir", type=str, help="[Deprecated] Models directory path (now using central config)")
    parser.add_argument("--model", type=str, choices=["sdxl-base", "sdxl-refiner", "sdxl-turbo", "sd1.5", "all"],
                      default="all", help="Model to download (default: all)")
    
    args = parser.parse_args()
    
    # Get the config
    config = setup_pipeline_environment("image_generation")
    
    # If models_dir is provided, use it (for backward compatibility)
    if args.models_dir:
        models_dir = Path(args.models_dir)
        print(f"Warning: --models-dir is deprecated. Using {models_dir} as requested, but consider using the central config.")
    else:
        models_dir = config.cache_dir
    
    print(f"Using cache directory: {models_dir}")
    
    # Ensure the cache directory exists
    models_dir.mkdir(parents=True, exist_ok=True)
    
    # Set up environment variables
    config.set_environment()
    
    success = False
    
    if args.model == "all":
        success = download_all_models(models_dir)
    elif args.model == "sdxl-base":
        target_path = models_dir / "stabilityai/stable-diffusion-xl-base-1.0"
        success = download_model("stabilityai/stable-diffusion-xl-base-1.0", target_path, "SDXL Base 1.0")
    elif args.model == "sdxl-refiner":
        target_path = models_dir / "stabilityai/stable-diffusion-xl-refiner-1.0"
        success = download_model("stabilityai/stable-diffusion-xl-refiner-1.0", target_path, "SDXL Refiner 1.0")
    elif args.model == "sdxl-turbo":
        target_path = models_dir / "stabilityai/sdxl-turbo"
        success = download_model("stabilityai/sdxl-turbo", target_path, "SDXL Turbo")
    elif args.model == "sd1.5":
        target_path = models_dir / "runwayml/stable-diffusion-v1-5"
        success = download_model("runwayml/stable-diffusion-v1-5", target_path, "Stable Diffusion 1.5")
    
    if success:
        print("\nDownload completed successfully!")
        print(f"Models are stored in: {models_dir}")
    else:
        print("\nDownload failed. Please check the error messages above.")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
