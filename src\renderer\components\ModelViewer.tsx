import React, { useRef, useEffect, useState, Suspense, useImperativeHandle, forwardRef, useCallback } from 'react';
import { Canvas, useFrame, useLoader, useThree } from '@react-three/fiber';
import { OrbitControls, Stage, useGLTF, Environment } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { PLYLoader } from 'three/examples/jsm/loaders/PLYLoader.js';
import { Mesh, PointsMaterial, Points, Vector3, Color, Material } from 'three';
import { Info, X, Clock, Settings, Cpu, Zap, Type, Lightbulb, Download, Save, Brush } from 'lucide-react';
import * as THREE from 'three';
import TextureInpainter from './TextureInpainterSimple';

interface ModelErrorBoundaryProps {
  isTextured: boolean;
  children: React.ReactNode;
}

interface ModelErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface RotatingMeshProps {
  isTextured: boolean;
  isDarkMode: boolean;
  shouldAutoRotate?: boolean;
}

interface GLBModelProps {
  modelUrl: string;
  isTextured: boolean;
  lightingSettings: LightingSettings;
  viewMode: 'textured' | 'wireframe' | 'white-mesh';
  shouldAutoRotate?: boolean;
  onModelLoaded?: () => void;
  onModelObjectLoaded?: (object: THREE.Object3D) => void;
}

interface PLYModelProps {
  modelUrl: string;
  isTextured: boolean;
  shouldAutoRotate?: boolean;
  onModelLoaded?: () => void;
  onModelObjectLoaded?: (object: THREE.Object3D) => void;
}

interface HSL {
  h: number;
  s: number;
  l: number;
}

// Lighting settings interface
interface LightingSettings {
  ambientIntensity: number;
  directionalIntensity: number;
  hemisphereIntensity: number;
  keyLightColor: string;
  fillLightColor: string;
  rimLightColor: string;
  enableShadows: boolean;
  shadowIntensity: number;
}

// Rendering settings interface
interface RenderingSettings {
  antialiasingLevel: number; // 0 = off, 2, 4, 8
  enableAntialiasing: boolean;
}

// View-mode-specific lighting settings
const getDefaultLightingForViewMode = (viewMode: 'textured' | 'wireframe' | 'white-mesh'): LightingSettings => {
  switch (viewMode) {
    case 'textured':
      return {
        ambientIntensity: .5,
        directionalIntensity: 3.0,
        hemisphereIntensity: .5,
        keyLightColor: "#ffffff",
        fillLightColor: "#ffffff",
        rimLightColor: "#ffffff",
        enableShadows: false,
        shadowIntensity: 0.5
      };
    case 'wireframe':
      return {
        ambientIntensity: 3.0,
        directionalIntensity: 3.0,
        hemisphereIntensity: 3.0,
        keyLightColor: "#ffffff",
        fillLightColor: "#ffffff",
        rimLightColor: "#ffffff",
        enableShadows: false,
        shadowIntensity: 0.3
      };
    case 'white-mesh':
      return {
        ambientIntensity: 0.0,
        directionalIntensity: 0.0,
        hemisphereIntensity: 0.0,
        keyLightColor: "#ffffff",
        fillLightColor: "#ffffff",
        rimLightColor: "#ffffff",
        enableShadows: false,
        shadowIntensity: 0.3
      };
    default:
      return {
        ambientIntensity: 3.0,
        directionalIntensity: 3.0,
        hemisphereIntensity: 3.0,
        keyLightColor: "#ffffff",
        fillLightColor: "#ffffff",
        rimLightColor: "#ffffff",
        enableShadows: false,
        shadowIntensity: 0.3
      };
  }
};

// Default lighting settings - will be overridden by view mode
const DEFAULT_LIGHTING: LightingSettings = getDefaultLightingForViewMode('textured');

// Default rendering settings
const DEFAULT_RENDERING: RenderingSettings = {
  antialiasingLevel: 4, // 4x MSAA by default
  enableAntialiasing: true
};

// Camera auto-fit component
const CameraAutoFit: React.FC<{
  targetObject?: THREE.Object3D | null;
  onBoundsCalculated?: (minDistance: number, maxDistance: number) => void;
}> = ({ targetObject, onBoundsCalculated }) => {
  const { camera, controls } = useThree();
  const [hasCalculated, setHasCalculated] = useState(false);

  useEffect(() => {
    if (targetObject && controls && camera instanceof THREE.PerspectiveCamera && !hasCalculated) {
      // Defer to next frame to allow any scale/position to apply
      const raf = requestAnimationFrame(() => {
        const timer = setTimeout(() => {
          console.log('CameraAutoFit: Fitting camera to object (refined)...');

          const box = new THREE.Box3().setFromObject(targetObject);
          const center = box.getCenter(new THREE.Vector3());
          const size = box.getSize(new THREE.Vector3());
          const maxDim = Math.max(size.x, size.y, size.z);

          if (maxDim > 0) {
            const fov = camera.fov * (Math.PI / 180);
            // Fit considering viewport aspect: choose the larger required distance
            const fitHeightDistance = (maxDim) / (2 * Math.tan(fov / 2));
            const fitWidthDistance = fitHeightDistance / (camera.aspect || 1);
            let distance = 1.1 * Math.max(fitHeightDistance, fitWidthDistance); // slight padding

            // Prefer keeping current distance if it's already closer (prevents "shrink after load")
            const currentDist = camera.position.distanceTo(center);
            if (currentDist && currentDist < distance) {
              distance = currentDist;
            }

            // Place camera along a pleasant diagonal
            const direction = new THREE.Vector3(3, 3, 6).normalize();
            const newPosition = center.clone().add(direction.multiplyScalar(distance));

            camera.position.copy(newPosition);
            camera.lookAt(center);

            if ('target' in controls && 'update' in controls) {
              (controls as any).target.copy(center);
              (controls as any).update();
            }

            const minDistance = Math.max(0.1, distance * 0.25);
            const maxDistance = distance * 6;

            if (onBoundsCalculated) {
              onBoundsCalculated(minDistance, maxDistance);
            }

            setHasCalculated(true);
            console.log('CameraAutoFit: Camera positioned at:', newPosition, 'looking at:', center);
            console.log('CameraAutoFit: Calculated bounds - min:', minDistance, 'max:', maxDistance);
          }
        }, 60);
        // Cleanup timer
        return () => clearTimeout(timer);
      });
      return () => cancelAnimationFrame(raf);
    }
  }, [targetObject, camera, controls, onBoundsCalculated, hasCalculated]);

  // Reset calculation flag when target object changes
  useEffect(() => {
    setHasCalculated(false);
  }, [targetObject]);

  return null;
};

// Error boundary for 3D model loading
class ModelErrorBoundary extends React.Component<ModelErrorBoundaryProps, ModelErrorBoundaryState> {
  constructor(props: ModelErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('ModelErrorBoundary: Error caught:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ModelErrorBoundary caught an error:', error);
    console.error('Error info:', errorInfo);
    console.error('Stack trace:', error.stack);
  }

  render() {
    if (this.state.hasError) {
      console.error('Model loading error, showing fallback:', this.state.error);
      // Return fallback sphere with error indication
      return (
        <mesh>
          <sphereGeometry args={[6.0, 32, 32]} />
          <meshStandardMaterial color="red" wireframe={!this.props.isTextured} />
        </mesh>
      );
    }

    return this.props.children;
  }
}

// Rotating mesh component
const RotatingMesh: React.FC<RotatingMeshProps> = ({ isTextured, isDarkMode, shouldAutoRotate }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state, delta) => {
    if (meshRef.current && shouldAutoRotate) {
      // Rotate slowly (0.3 radians per second)
      meshRef.current.rotation.y += delta * 0.3;
    }
  });

  return (
    <mesh ref={meshRef}>
      <sphereGeometry args={[6.0, 32, 32]} />
      <meshStandardMaterial
        color={isDarkMode ? '#303030' : '#404040'}
        wireframe={!isTextured}
      />
    </mesh>
  );
};

// Safe GLB Model component - memoized to prevent unnecessary re-renders
const GLBModel = React.memo<GLBModelProps>(({ modelUrl, isTextured, lightingSettings, viewMode, shouldAutoRotate, onModelLoaded, onModelObjectLoaded }) => {
  console.log('GLBModel: Starting to load:', modelUrl);
  console.log('GLBModel: Model URL type:', typeof modelUrl);
  console.log('GLBModel: Model URL length:', modelUrl?.length);
  console.log('GLBModel: Model URL starts with data:', modelUrl?.startsWith('data:'));

  const modelRef = useRef<THREE.Group>(null);
  const [modelScale, setModelScale] = useState(1.0);
  const [modelPosition, setModelPosition] = useState<[number, number, number]>([0, 0, 0]);
  const [isAutoFitted, setIsAutoFitted] = useState(false);
  const originalMaterialsRef = useRef<Map<THREE.Mesh, THREE.Material | THREE.Material[]>>(new Map());

  // Use useGLTF - this must be called unconditionally
  console.log('GLBModel: Calling useGLTF...');
  const gltf = useGLTF(modelUrl);
  const scene = gltf?.scene;

  console.log('GLBModel: useGLTF result:', { gltf, scene, hasScene: !!scene });
  if (gltf && gltf.scene) {
    console.log('GLBModel: Scene children count:', gltf.scene.children.length);
    console.log('GLBModel: Scene type:', gltf.scene.type);
  }

  // Reset auto-fit state when model changes
  useEffect(() => {
    setIsAutoFitted(false);
    setModelScale(1.0);
    setModelPosition([0, 0, 0]);
  }, [modelUrl]);

  // Auto-fit model to viewport with improved sizing and positioning
  useEffect(() => {
    if (scene && !isAutoFitted) {
      console.log('GLBModel: Auto-fitting model to viewport...');

      // Ensure all geometries are computed
      scene.traverse((child) => {
        if (child instanceof THREE.Mesh && child.geometry) {
          child.geometry.computeBoundingBox();
        }
      });

      const box = new THREE.Box3().setFromObject(scene);
      const center = box.getCenter(new THREE.Vector3());
      const size = box.getSize(new THREE.Vector3());
      const maxDim = Math.max(size.x, size.y, size.z);

      console.log('GLBModel: Bounding box:', { center, size, maxDim });

      // Calculate scale to fit model nicely in viewport
      // Target size of 12.0 units for much better visibility and larger initial appearance
      const targetSize = 12.0;
      const scale = maxDim > 0 ? targetSize / maxDim : 1.0;

      // Center the model perfectly
      const position = [
        -center.x * scale,
        -center.y * scale,
        -center.z * scale
      ] as [number, number, number];

      console.log('GLBModel: Applied scale:', scale, 'position:', position);

      setModelScale(scale);
      setModelPosition(position);
      setIsAutoFitted(true);
    }
  }, [scene, isAutoFitted]);

  // Enhanced material handling for better lighting response
  useEffect(() => {
    if (scene) {
      scene.traverse((node: THREE.Object3D) => {
        if ((node as Mesh).isMesh) {
          const mesh = node as Mesh;
          mesh.castShadow = true;
          mesh.receiveShadow = true;

          // Store original materials if not already stored
          if (!originalMaterialsRef.current.has(mesh)) {
            originalMaterialsRef.current.set(mesh, mesh.material);
          }

          // Get the original material
          const originalMaterial = originalMaterialsRef.current.get(mesh);
          if (!originalMaterial) return;

          // Handle different view modes
          if (viewMode === 'white-mesh') {
            // Create white material for white mesh mode - force bright white
            const whiteMaterial = new THREE.MeshStandardMaterial({
              color: new THREE.Color(1, 1, 1), // Explicit white color
              roughness: 0.5,
              metalness: 0.0,
              wireframe: false,
              side: THREE.DoubleSide,
              transparent: false,
              opacity: 1.0,
              emissive: new THREE.Color(0.1, 0.1, 0.1), // Slight emissive to ensure visibility
              emissiveIntensity: 0.1
            });

            // Force material update
            whiteMaterial.needsUpdate = true;

            // Apply to all materials if it's an array, ensuring each gets a fresh instance
            if (Array.isArray(mesh.material)) {
              mesh.material = mesh.material.map(() => {
                const clonedMaterial = whiteMaterial.clone();
                clonedMaterial.needsUpdate = true;
                return clonedMaterial;
              });
            } else {
              mesh.material = whiteMaterial.clone();
              mesh.material.needsUpdate = true;
            }

            // Force geometry update to ensure material is applied
            if (mesh.geometry) {
              mesh.geometry.computeVertexNormals();
            }

            return; // Skip further processing for white mesh mode
          }

          // For other modes, work with original materials
          mesh.material = originalMaterial;
          const materials = Array.isArray(mesh.material) ? mesh.material : [mesh.material];

          materials.forEach((material, index) => {
            // Convert to MeshStandardMaterial if it's not already
            if (!(material instanceof THREE.MeshStandardMaterial)) {
              const standardMaterial = new THREE.MeshStandardMaterial();

              // Copy properties from original material
              if (material instanceof THREE.MeshBasicMaterial) {
                standardMaterial.color.copy(material.color);
                standardMaterial.map = material.map;
                standardMaterial.transparent = material.transparent;
                standardMaterial.opacity = material.opacity;
              } else if (material instanceof THREE.MeshPhongMaterial) {
                standardMaterial.color.copy(material.color);
                standardMaterial.map = material.map;
                standardMaterial.transparent = material.transparent;
                standardMaterial.opacity = material.opacity;
              }

              // Set enhanced standard material properties for better visibility
              standardMaterial.roughness = 0.3;
              standardMaterial.metalness = 0.1;
              standardMaterial.envMapIntensity = 1.5; // Increased for better reflections
              standardMaterial.wireframe = viewMode === 'wireframe'; // Apply wireframe mode
              standardMaterial.needsUpdate = true;

              // Update the mesh material
              if (Array.isArray(mesh.material)) {
                mesh.material[index] = standardMaterial;
              } else {
                mesh.material = standardMaterial;
              }
            } else {
              // Enhance existing MeshStandardMaterial with better visibility settings
              const standardMaterial = material as THREE.MeshStandardMaterial;

              // Apply wireframe mode
              standardMaterial.wireframe = viewMode === 'wireframe';

              // Enhance material properties for better visibility
              if (viewMode === 'textured') {
                standardMaterial.roughness = Math.max(standardMaterial.roughness, 0.2);
                standardMaterial.metalness = Math.max(standardMaterial.metalness, 0.05);
                standardMaterial.envMapIntensity = 1.5;

                // Enhanced color correction for very dark materials
                if (standardMaterial.color instanceof Color) {
                  const hsl: HSL = { h: 0, s: 0, l: 0 };
                  standardMaterial.color.getHSL(hsl);

                  // More aggressive lightness correction for dark models
                  if (hsl.l < 0.25) {
                    standardMaterial.color.setHSL(hsl.h, hsl.s, Math.max(hsl.l, 0.35));
                  }

                  // Also clamp RGB values directly to prevent completely black materials
                  const color = standardMaterial.color;
                  color.r = Math.max(color.r, 0.2);
                  color.g = Math.max(color.g, 0.2);
                  color.b = Math.max(color.b, 0.2);
                }
              }

              standardMaterial.needsUpdate = true;
            }

            // Enhanced color correction for very dark materials (only in textured mode)
            if (viewMode === 'textured' && 'color' in material && material.color instanceof Color) {
              const hsl: HSL = { h: 0, s: 0, l: 0 };
              material.color.getHSL(hsl);

              // More aggressive lightness correction for dark models
              if (hsl.l < 0.25) {
                material.color.setHSL(hsl.h, hsl.s, Math.max(hsl.l, 0.35));
              }

              // Also clamp RGB values directly to prevent completely black materials
              const color = material.color;
              color.r = Math.max(color.r, 0.2);
              color.g = Math.max(color.g, 0.2);
              color.b = Math.max(color.b, 0.2);
            }
          });
        }
      });
    }
  }, [scene, viewMode]);

  // Update materials when lightingSettings changes
  useEffect(() => {
    if (scene && lightingSettings) {
      scene.traverse((node: THREE.Object3D) => {
        if ((node as Mesh).isMesh) {
          const mesh = node as Mesh;
          mesh.castShadow = true;
          mesh.receiveShadow = true;
          const materials = Array.isArray(mesh.material) ? mesh.material : [mesh.material];
          materials.forEach((material) => {
            // Update lighting-related properties
            if (material instanceof THREE.MeshStandardMaterial) {
              material.envMapIntensity = 1.5;
              material.needsUpdate = true;
            }
            // Optionally, update color or other properties based on lightingSettings
            // e.g., material.color.set(lightingSettings.keyLightColor)
          });
        }
      });
    }
  }, [scene, lightingSettings]);

  useFrame((state, delta) => {
    if (modelRef.current && shouldAutoRotate) {
      modelRef.current.rotation.y += delta * 0.3;
    }
  });

  // Check if scene is valid AFTER all hooks have been called
  if (!scene) {
    console.error('GLBModel: No scene found in GLTF, returning fallback sphere');
    console.error('GLBModel: GLTF object:', gltf);
    console.error('GLBModel: Model URL that failed:', modelUrl);
    return (
      <mesh>
        <sphereGeometry args={[6.0, 32, 32]} />
        <meshStandardMaterial color="yellow" wireframe={!isTextured} />
      </mesh>
    );
  }

  useEffect(() => {
    if (scene) {
      if (onModelLoaded) {
        onModelLoaded();
      }
      if (onModelObjectLoaded) {
        onModelObjectLoaded(scene);
      }
    }
  }, [scene, onModelLoaded, onModelObjectLoaded]);

  return (
    <primitive
      ref={modelRef}
      object={scene}
      scale={modelScale}
      position={modelPosition}
    />
  );
});

// PLY Model component (for point clouds) - memoized to prevent unnecessary re-renders
const PLYModel = React.memo<PLYModelProps>(({ modelUrl, isTextured, shouldAutoRotate, onModelLoaded, onModelObjectLoaded }) => {
  console.log('PLYModel loading:', modelUrl);

  // Use useLoader with a function to ensure PLYLoader is instantiated correctly if needed
  const geometry = useLoader(PLYLoader, modelUrl);
  const pointsRef = useRef<THREE.Points>(null);
  const [pointsMaterial, setPointsMaterial] = useState<THREE.PointsMaterial | null>(null);

  // Initialize points material
  useEffect(() => {
    if (geometry) {
      const material = new THREE.PointsMaterial({
        size: 0.003,
        vertexColors: true,
        sizeAttenuation: true,
        alphaTest: 0.1,
        transparent: false,
        color: 0xffffff,
      });

      setPointsMaterial(material);

      // Auto-fit model with improved centering
      geometry.computeBoundingSphere();
      geometry.computeBoundingBox();

      if (geometry.boundingSphere && geometry.boundingBox && pointsRef.current) {
        console.log('PLYModel: Auto-fitting point cloud to viewport...');

        const sphere = geometry.boundingSphere;
        const box = geometry.boundingBox;
        const center = box.getCenter(new THREE.Vector3());

        // Use a target size of 12.0 units for consistency with GLB models
        const targetSize = 12.0;
        const scale = sphere.radius > 0 ? targetSize / sphere.radius : 1.0;

        console.log('PLYModel: Sphere radius:', sphere.radius, 'Scale:', scale, 'Center:', center);

        pointsRef.current.scale.set(scale, scale, scale);
        // Center the model properly
        pointsRef.current.position.set(
          -center.x * scale,
          -center.y * scale,
          -center.z * scale
        );

        // Notify parent about loaded model object for camera auto-fit
        if (onModelObjectLoaded) {
          onModelObjectLoaded(pointsRef.current);
        }
      }

      // Call onModelLoaded callback
      if (onModelLoaded) {
        onModelLoaded();
      }
    }
  }, [geometry, onModelLoaded, onModelObjectLoaded]);

  // This auto-fitting logic is now handled in the material initialization useEffect above

  useFrame((state, delta) => {
    if (pointsRef.current && shouldAutoRotate) {
      pointsRef.current.rotation.y += delta * 0.3;
    }
  });

  if (!geometry) {
    console.warn('PLYModel: Geometry not loaded yet, returning null.');
    return null;
  }

  useEffect(() => { if (geometry && onModelLoaded) onModelLoaded(); }, [geometry]);

  return (
    <points ref={pointsRef} geometry={geometry} material={pointsMaterial || undefined} castShadow>
      {/* Fallback material if state is not ready */}
      {!pointsMaterial && <pointsMaterial attach="material" size={0.005} color="magenta" />}
    </points>
  );
});

interface GenerationStats {
  generationMode?: 'text-to-3d' | 'image-to-3d';
  prompt?: string;
  enhancedPrompt?: string;
  imageModel?: string;
  settings?: {
    ss_steps?: number;
    ss_cfg_strength?: number;
    slat_steps?: number;
    slat_cfg_strength?: number;
    seed?: number;
    simplify?: number;
    texture_size?: number;
    enable_lighting_optimizer?: boolean;
    enable_delighter?: boolean;
    delighter_quality?: string;
  };
  fileInfo?: {
    type?: string;
    size?: number;
    vertices?: number;
    faces?: number;
    width?: number;
    height?: number;
  };
  timing?: {
    totalTime?: number;
    textToImageTime?: number;
    modelGenerationTime?: number;
    delighterTime?: number;
  };
}

interface ModelViewerProps {
  modelUrl?: string | null;
  filePath?: string;
  videoUrl?: string;
  isTextured?: boolean;
  isDarkMode?: boolean;
  viewMode?: 'textured' | 'wireframe' | 'white-mesh';
  generationStats?: GenerationStats;
  // Props for saving as project
  originalImagePath?: string;
  prompt?: string;
  settings?: any;
  onProjectSaved?: () => void;
  onModelLoaded?: () => void;
}

export interface ModelViewerHandle {
  getScreenshot: (size?: number) => string | null;
}

export const ModelViewer = forwardRef<ModelViewerHandle, ModelViewerProps>(({
  modelUrl,
  filePath,
  videoUrl,
  isTextured = true,
  isDarkMode = false,
  viewMode: externalViewMode = 'textured',
  generationStats,
  originalImagePath,
  prompt,
  settings,
  onProjectSaved,
  onModelLoaded,
}, ref) => {
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [fileType, setFileType] = useState<string | null>(null);
  const [isInfoExpanded, setIsInfoExpanded] = useState(false);
  const [isLightingMenuOpen, setIsLightingMenuOpen] = useState(false);
  const [isRenderingMenuOpen, setIsRenderingMenuOpen] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [activeTab, setActiveTab] = useState('info');
  const infoPanelRef = useRef<HTMLDivElement>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isInpaintingMode, setIsInpaintingMode] = useState(false);
  const modelGroupRef = useRef<THREE.Group>(null);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [projectName, setProjectName] = useState('');
  const webglCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const viewMode = externalViewMode; // Use the external viewMode prop instead of internal state
  const [loadedModelObject, setLoadedModelObject] = useState<THREE.Object3D | null>(null);
  const [cameraLimits, setCameraLimits] = useState({ minDistance: 1, maxDistance: 20 });
  const [boundsCalculated, setBoundsCalculated] = useState(false);

  // Handle bounds calculation from CameraAutoFit
  const handleBoundsCalculated = useCallback((minDistance: number, maxDistance: number) => {
    setCameraLimits({ minDistance, maxDistance });
    setBoundsCalculated(true);
    console.log('ModelViewer: Updated camera limits - min:', minDistance, 'max:', maxDistance);
  }, []);

  // Reset camera limits when model changes
  useEffect(() => {
    setCameraLimits({ minDistance: 1, maxDistance: 20 });
    setBoundsCalculated(false);
    setLoadedModelObject(null);
  }, [modelUrl, filePath]);

  // Expose getScreenshot method to parent
  useImperativeHandle(ref, () => ({
    getScreenshot: (size = 256) => {
      const canvas = webglCanvasRef.current;
      if (!canvas) return null;
      // Create an offscreen canvas for resizing
      const offscreen = document.createElement('canvas');
      offscreen.width = size;
      offscreen.height = size;
      const ctx = offscreen.getContext('2d');
      if (!ctx) return null;
      ctx.fillStyle = isDarkMode ? '#222' : '#fff';
      ctx.fillRect(0, 0, size, size);
      ctx.drawImage(canvas, 0, 0, size, size);
      return offscreen.toDataURL('image/png');
    }
  }), [isDarkMode]);

  // Download handler
  const handleDownloadModel = () => {
    if (filePath) {
      window.electronAPI.downloadFile(filePath);
    }
  };

  // Save as project handler
  const handleSaveAsProject = () => {
    setProjectName(`3D Model ${new Date().toLocaleDateString()}`);
    setShowSaveDialog(true);
  };

  const handleConfirmSave = async () => {
    if (!filePath || !projectName.trim()) return;

    setIsSaving(true);
    try {
      // Merge all settings into generationStats.settings
      const mergedSettings = { ...generationStats?.settings, ...settings };
      // Try to get image resolution from the original image (if available)
      let width, height;
      if (originalImagePath) {
        try {
          // Dynamically import image-size only when needed
          const sizeOf = (await import('image-size')).default;
          const fs = window.require ? window.require('fs') : null;
          if (fs && fs.existsSync(originalImagePath)) {
            const buffer = fs.readFileSync(originalImagePath);
            const dimensions = sizeOf(buffer);
            width = dimensions.width;
            height = dimensions.height;
          }
        } catch (e) {
          console.warn('Could not determine image resolution:', e);
        }
      }
      // Merge fileInfo
      const mergedFileInfo = {
        ...generationStats?.fileInfo,
        ...(width && height ? { width, height } : {})
      };
      // Compose newGenerationStats
      const newGenerationStats = {
        ...generationStats,
        settings: mergedSettings,
        fileInfo: mergedFileInfo
      };
      const result = await window.electronAPI.createProjectFrom3DModel({
        name: projectName.trim(),
        type: 'image-to-3d',
        modelFilePath: filePath,
        videoFilePath: videoUrl || undefined,
        originalImagePath: originalImagePath,
        prompt: prompt,
        settings: settings,
        generationStats: newGenerationStats
      });

      if (result.success) {
        setShowSaveDialog(false);
        setProjectName('');
        if (onProjectSaved) {
          onProjectSaved();
        }
      } else {
        console.error('Failed to save project:', result.error);
        // You could add error state handling here
      }
    } catch (error) {
      console.error('Error saving project:', error);
    } finally {
      setIsSaving(false);
    }
  };
  console.log("ModelViewer received modelUrl:", modelUrl);
  console.log("ModelViewer received filePath:", filePath);

  // Load saved lighting settings or use view-mode-specific defaults
  const [lightingSettings, setLightingSettings] = useState<LightingSettings>(() => {
    const saved = localStorage.getItem('modelViewerLighting');
    return saved ? JSON.parse(saved) : getDefaultLightingForViewMode(viewMode);
  });

  // Update lighting settings when view mode changes
  useEffect(() => {
    const newLightingSettings = getDefaultLightingForViewMode(viewMode);
    setLightingSettings(newLightingSettings);
  }, [viewMode]);

  // Load saved rendering settings or use defaults
  const [renderingSettings, setRenderingSettings] = useState<RenderingSettings>(() => {
    const saved = localStorage.getItem('modelViewerRendering');
    return saved ? JSON.parse(saved) : DEFAULT_RENDERING;
  });

  // Save lighting settings when they change
  useEffect(() => {
    localStorage.setItem('modelViewerLighting', JSON.stringify(lightingSettings));
  }, [lightingSettings]);

  // Save rendering settings when they change
  useEffect(() => {
    localStorage.setItem('modelViewerRendering', JSON.stringify(renderingSettings));
  }, [renderingSettings]);

  const lightingMenuRef = useRef<HTMLDivElement>(null);
  const renderingMenuRef = useRef<HTMLDivElement>(null);

  // Close lighting menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (lightingMenuRef.current && !lightingMenuRef.current.contains(event.target as Node)) {
        setIsLightingMenuOpen(false);
      }
      if (renderingMenuRef.current && !renderingMenuRef.current.contains(event.target as Node)) {
        setIsRenderingMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [lightingMenuRef, renderingMenuRef]);

  // Handle 3D texture inpainting
  const handleInpainting = async (maskTexture: THREE.Texture, prompt: string) => {
    if (!filePath) {
      throw new Error('No model file path available');
    }

    try {
      console.log('Starting 3D texture inpainting...');

      // Convert mask texture to image data
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      if (!context) throw new Error('Could not create canvas context');

      canvas.width = 1024;
      canvas.height = 1024;

      // Draw mask texture to canvas
      const maskImage = maskTexture.image;
      context.drawImage(maskImage, 0, 0, canvas.width, canvas.height);

      // Convert canvas to blob
      const maskBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/png');
      });

      // Convert blob to buffer
      const maskBuffer = await maskBlob.arrayBuffer();

      // Call inpainting via IPC
      const result = await window.electronAPI.runPipeline('ImageGeneration', {
        action: 'inpaint_texture',
        glb_path: filePath,
        mask_buffer: new Uint8Array(maskBuffer),
        prompt: prompt,
        strength: 0.8
      });

      if (result.success && result.output_path) {
        console.log('3D texture inpainting completed:', result.output_path);
        // Reload the model with the new texture
        window.location.reload(); // Simple approach - could be improved
      } else {
        throw new Error(result.error || 'Inpainting failed');
      }

    } catch (error) {
      console.error('Inpainting error:', error);
      throw error;
    }
  };

  // Determine file type
  const getFileExtension = (path: string | undefined) => {
    if (!path) return '';
    return path.split('.').pop()?.toLowerCase() || '';
  };

  const renderModel = () => {
    if (!modelUrl) {
      console.log('No modelUrl provided, showing default sphere');
      return <RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} shouldAutoRotate={shouldAutoRotate} />;
    }

    const extension = getFileExtension(filePath);
    console.log('Model URL:', modelUrl, 'File path:', filePath, 'Detected file type:', extension);

    try {
      console.log(`renderModel: Attempting to render ${filePath}`);

      if (extension === 'glb' || extension === 'gltf') {
        return (
          <React.Suspense fallback={<RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} shouldAutoRotate={shouldAutoRotate} />}>
            <ModelErrorBoundary isTextured={isTextured}>
              <GLBModel
                key={filePath}
                modelUrl={modelUrl}
                isTextured={isTextured}
                lightingSettings={lightingSettings}
                viewMode={viewMode}
                shouldAutoRotate={shouldAutoRotate}
                onModelLoaded={onModelLoaded}
                onModelObjectLoaded={setLoadedModelObject}
              />
            </ModelErrorBoundary>
          </React.Suspense>
        );
      }

      if (extension === 'ply') {
        return (
          <React.Suspense fallback={<RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} shouldAutoRotate={shouldAutoRotate} />}>
            <PLYModel
              key={filePath}
              modelUrl={modelUrl}
              isTextured={isTextured}
              shouldAutoRotate={shouldAutoRotate}
              onModelLoaded={onModelLoaded}
              onModelObjectLoaded={setLoadedModelObject}
            />
          </React.Suspense>
        );
      }

      console.warn(`renderModel: Unsupported file extension: ${extension} from path: ${filePath}`);
      return <RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} shouldAutoRotate={shouldAutoRotate} />;
    } catch (error) {
      console.error("Error in renderModel:", error);
      return <RotatingMesh isTextured={isTextured} isDarkMode={isDarkMode} shouldAutoRotate={shouldAutoRotate} />;
    }
  };

  // Show error state if there's a loading error
  if (loadingError) {
    return (
      <div className={`w-full h-full rounded-lg flex items-center justify-center ${
        isDarkMode
          ? 'bg-gradient-to-br from-gray-800 to-gray-900 text-white'
          : 'bg-gradient-to-br from-white to-gray-100 text-gray-800'
      }`}>
        <div className="text-center p-4">
          <div className="text-red-500 mb-2">⚠️ Model Loading Error</div>
          <div className="text-sm">{loadingError}</div>
          <div className="text-xs mt-2 opacity-70">URL: {modelUrl}</div>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className={`w-full h-full rounded-lg flex items-center justify-center ${
        isDarkMode
          ? 'bg-gradient-to-br from-gray-800 to-gray-900 text-white'
          : 'bg-gradient-to-br from-white to-gray-100 text-gray-800'
      }`}>
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <div className="text-sm">Testing model URL...</div>
        </div>
      </div>
    );
  }

  // Format file size
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // Format time duration
  const formatTime = (seconds?: number) => {
    if (!seconds) return 'Unknown';
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const controlsRef = useRef();

  // Auto-rotate pause/resume: pause on user interaction for 5s, then resume
  const [autoRotateEnabled, setAutoRotateEnabled] = useState(true);
  const interactionTimerRef = useRef<any>(null);
  const markUserInteraction = useCallback(() => {
    // Any manual rotate/pan/zoom pauses auto-rotation immediately
    if (interactionTimerRef.current) {
      clearTimeout(interactionTimerRef.current);
      interactionTimerRef.current = null;
    }
    if (autoRotateEnabled) setAutoRotateEnabled(false);
    // Resume after 5s of inactivity
    interactionTimerRef.current = setTimeout(() => {
      setAutoRotateEnabled(true);
      interactionTimerRef.current = null;
    }, 5000);
  }, [autoRotateEnabled]);
  useEffect(() => {
    return () => {
      if (interactionTimerRef.current) clearTimeout(interactionTimerRef.current);
    };
  }, []);

  const [lighting, setLighting] = useState(DEFAULT_LIGHTING);
  const shouldAutoRotate = autoRotateEnabled && !isInpaintingMode;

  // Attach low-level listeners to detect real user interactions and pause auto-rotate immediately
  useEffect(() => {
    const controls: any = controlsRef.current as any;
    if (!controls) return;
    const dom: HTMLElement | undefined = controls.domElement;
    if (!dom) return;

    const handleUser = () => {
      // stop internal autorotate immediately (imperative) and start resume timer
      controls.autoRotate = false;
      markUserInteraction();
    };

    dom.addEventListener('pointerdown', handleUser, { passive: true } as any);
    dom.addEventListener('wheel', handleUser, { passive: true } as any);
    dom.addEventListener('touchstart', handleUser, { passive: true } as any);
    controls.addEventListener('start', handleUser);
    controls.addEventListener('end', handleUser);

    return () => {
      dom.removeEventListener('pointerdown', handleUser as any);
      dom.removeEventListener('wheel', handleUser as any);
      dom.removeEventListener('touchstart', handleUser as any);
      controls.removeEventListener('start', handleUser as any);
      controls.removeEventListener('end', handleUser as any);
    };
  }, [markUserInteraction]);

  // Keep the underlying controls autoRotate flag in sync with state
  useEffect(() => {
    const controls: any = controlsRef.current as any;
    if (controls) {
      controls.autoRotate = !isInpaintingMode && autoRotateEnabled;
    }
  }, [autoRotateEnabled, isInpaintingMode]);

  // For informational popover
  const [isInfoVisible, setIsInfoVisible] = useState(false);

  return (
    <div className={`relative w-full h-full rounded-lg ${isDarkMode ? 'dark' : ''}`}>
      <Canvas
        shadows={lightingSettings.enableShadows}
        camera={{
          position: [8, 8, 15], // Moved camera further back to accommodate larger models
          fov: 45, // Slightly narrower field of view for better perspective
          near: 0.1,
          far: 200
        }}
        className="w-full h-full rounded-lg"
        gl={{
          outputColorSpace: THREE.SRGBColorSpace,
          toneMapping: THREE.ACESFilmicToneMapping,
          toneMappingExposure: 1.2,
          antialias: renderingSettings.enableAntialiasing
        }}
        onCreated={({ gl }) => {
          // Store canvas reference
          webglCanvasRef.current = gl.domElement;

          // Configure additional anti-aliasing if enabled
          if (renderingSettings.enableAntialiasing && renderingSettings.antialiasingLevel > 0) {
            gl.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Limit pixel ratio for performance
          }
        }}
        key={`${renderingSettings.enableAntialiasing}-${renderingSettings.antialiasingLevel}`}
      >
        <Suspense fallback={null}>
          <ambientLight intensity={lightingSettings.ambientIntensity} />
          <hemisphereLight
            args={[new THREE.Color(0xffffff), new THREE.Color(0x444444), lightingSettings.hemisphereIntensity]}
          />
          <directionalLight
            castShadow={lightingSettings.enableShadows}
            position={[5, 5, 5]}
            intensity={lightingSettings.directionalIntensity}
            color={lightingSettings.keyLightColor}
          />
          <directionalLight
            position={[-5, 3, -5]}
            intensity={lightingSettings.directionalIntensity * 0.5}
            color={lightingSettings.fillLightColor}
          />
          <directionalLight
            position={[0, 3, -5]}
            intensity={lightingSettings.directionalIntensity * 0.2}
            color={lightingSettings.rimLightColor}
          />


          {/* Additional point lights for better GLB model illumination */}
          <pointLight position={[3, 3, 3]} intensity={1.0} color="#ffffff" />
          <pointLight position={[-3, 3, 3]} intensity={0.8} color="#ffffff" />
          <pointLight position={[0, -2, 3]} intensity={0.6} color="#ffffff" />

          <Environment preset={isDarkMode ? "sunset" : "studio"} />

              {renderModel()}

          {/* Camera auto-fit component - will automatically position camera when model loads */}
          <CameraAutoFit
            targetObject={loadedModelObject}
            onBoundsCalculated={handleBoundsCalculated}
          />
        </Suspense>
        <OrbitControls
          ref={controlsRef}
          makeDefault
          autoRotate={!isInpaintingMode && autoRotateEnabled} // Pause on user interaction, resume after 5s
          autoRotateSpeed={0.8}
          minDistance={boundsCalculated ? cameraLimits.minDistance : 0.5} // Use calculated bounds or conservative default
          maxDistance={boundsCalculated ? cameraLimits.maxDistance : 50} // Use calculated bounds or generous default
          enablePan={true} // Enable panning for better control
          enableDamping={true} // Smooth camera movement
          dampingFactor={0.1} // Slightly higher damping for stability
          target={[0, 0, 0]} // Center the orbit target
          enableZoom={true} // Ensure zoom is enabled
          enableRotate={true} // Ensure rotation is enabled
          onStart={markUserInteraction}
          onEnd={markUserInteraction}
        />
      </Canvas>

      {/* 3D Texture Inpainting Component */}
      {isInpaintingMode && (
        <TextureInpainter
          isActive={isInpaintingMode}
          onClose={() => setIsInpaintingMode(false)}
          isDarkMode={isDarkMode}
        />
      )}

      {/* Top-left icons */}
      <div className="absolute top-4 left-4 flex flex-col space-y-2">
        <button
          onClick={() => setIsInfoExpanded(!isInfoExpanded)}
          className={`p-2 rounded-full transition-colors ${
            isInfoExpanded
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
          }`}
          title="Toggle Info Panel"
        >
          <Info size={20} />
        </button>

        <button
            onClick={() => setIsLightingMenuOpen(!isLightingMenuOpen)}
          className={`p-2 rounded-full transition-colors ${
            isLightingMenuOpen
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
          }`}
          title="Toggle Lighting Settings"
        >
          <Lightbulb size={20} />
        </button>

        <button
          onClick={() => setIsRenderingMenuOpen(!isRenderingMenuOpen)}
          className={`p-2 rounded-full transition-colors ${
            isRenderingMenuOpen
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
          }`}
          title="Toggle Rendering Settings"
        >
          <Settings size={20} />
        </button>

        <button
          onClick={() => setIsInpaintingMode(!isInpaintingMode)}
          disabled={!filePath || getFileExtension(filePath) !== 'glb'}
          className={`p-2 rounded-full transition-colors ${
            isInpaintingMode
              ? 'bg-purple-500 text-white'
              : filePath && getFileExtension(filePath) === 'glb'
              ? 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
          }`}
          title="3D Texture Inpainting (GLB models only)"
        >
          <Brush size={20} />
        </button>

        <button
          onClick={handleDownloadModel}
          disabled={!filePath}
          className={`p-2 rounded-full transition-colors ${
            filePath
              ? 'bg-green-500 hover:bg-green-600 text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
          }`}
          title="Download 3D Model (GLB)"
        >
          <Download size={20} />
        </button>

        <button
          onClick={handleSaveAsProject}
          disabled={!filePath}
          className={`p-2 rounded-full transition-colors ${
            filePath
              ? 'bg-blue-500 hover:bg-blue-600 text-white'
              : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
          }`}
          title="Save As Project"
        >
          <Save size={20} />
        </button>
      </div>

      {/* Info Panel */}
      {isInfoExpanded && (
        <div
          ref={infoPanelRef}
          className="absolute top-4 left-16 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg w-64 text-sm text-gray-700 dark:text-gray-300"
        >
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-bold text-lg text-gray-900 dark:text-white">Generation Info</h3>
            <button onClick={() => setIsInfoExpanded(false)} className="text-gray-500 hover:text-gray-800 dark:hover:text-white">
              <X size={18} />
            </button>
          </div>
          {/* Tabs */}
          <div className="flex border-b border-gray-200 dark:border-gray-700 mb-2">
            <button
              onClick={() => setActiveTab('info')}
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === 'info'
                  ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              Info
            </button>
            <button
              onClick={() => setActiveTab('stats')}
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === 'stats'
                  ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              Stats
            </button>
          </div>

          {/* Content */}
          {activeTab === 'info' && (
            <div className="space-y-2">
              <p><strong>View Mode:</strong>
                <span className={`ml-2 px-2 py-1 rounded text-xs ${
                  viewMode === 'textured' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                  viewMode === 'wireframe' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                }`}>
                  {viewMode === 'textured' ? 'Textured' : viewMode === 'wireframe' ? 'Wireframe' : 'White Mesh'}
                </span>
              </p>
              <p><strong>Mode:</strong> {generationStats?.generationMode?.replace('-', ' ')}</p>
              {generationStats?.imageModel && <p><strong>Image Model:</strong> {generationStats.imageModel}</p>}
              {/* Image Resolution */}
              {generationStats?.fileInfo?.width && generationStats?.fileInfo?.height && (
                <p><strong>Resolution:</strong> {generationStats.fileInfo.width} × {generationStats.fileInfo.height}</p>
              )}
              <p><strong>Prompt:</strong></p>
              <p className="p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs">{generationStats?.prompt || 'N/A'}</p>
              {/* Generation Settings */}
              {generationStats?.settings && (
                <div>
                  <p className="mt-2 font-semibold">Generation Settings:</p>
                  <table className="text-xs w-full mt-1">
                    <tbody>
                      {Object.entries(generationStats.settings).map(([key, value]) => (
                        <tr key={key}>
                          <td className="pr-2 text-gray-500 dark:text-gray-400">{key.replace(/_/g, ' ')}:</td>
                          <td className="text-gray-900 dark:text-gray-100">{String(value)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
          {activeTab === 'stats' && (
            <div className="space-y-2">
              <p><strong>Total Time:</strong> {formatTime(generationStats?.timing?.totalTime)}</p>
              <p><strong>Vertices:</strong> {generationStats?.fileInfo?.vertices?.toLocaleString()}</p>
              <p><strong>Faces:</strong> {generationStats?.fileInfo?.faces?.toLocaleString()}</p>
              <p><strong>File Size:</strong> {formatFileSize(generationStats?.fileInfo?.size)}</p>
            </div>
          )}
          </div>
        )}

      {/* Lighting Settings Panel */}
        {isLightingMenuOpen && (
        <div
          ref={lightingMenuRef}
          className="absolute top-4 right-24 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg w-72 text-sm text-gray-700 dark:text-gray-300"
        >
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-bold text-lg text-gray-900 dark:text-white">Lighting Settings</h3>
            <button onClick={() => setIsLightingMenuOpen(false)} className="text-gray-500 hover:text-gray-800 dark:hover:text-white">
              <X size={18} />
            </button>
          </div>

            <div className="space-y-3">
            <LightingSlider
              label="Ambient"
                  value={lightingSettings.ambientIntensity}
              onChange={(val) => setLightingSettings(p => ({...p, ambientIntensity: val}))}
            />
            <LightingSlider
              label="Directional"
                  value={lightingSettings.directionalIntensity}
              onChange={(val) => setLightingSettings(p => ({...p, directionalIntensity: val}))}
            />
            <LightingSlider
              label="Hemisphere"
                  value={lightingSettings.hemisphereIntensity}
              onChange={(val) => setLightingSettings(p => ({...p, hemisphereIntensity: val}))}
            />

            {/* Shadow Controls */}
            <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium">Enable Shadows</label>
                <button
                  onClick={() => setLightingSettings(p => ({...p, enableShadows: !p.enableShadows}))}
                  className={`w-12 h-6 rounded-full transition-colors ${
                    lightingSettings.enableShadows
                      ? 'bg-blue-500'
                      : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                >
                  <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                    lightingSettings.enableShadows ? 'translate-x-6' : 'translate-x-0.5'
                  }`} />
                </button>
              </div>

              {lightingSettings.enableShadows && (
                <LightingSlider
                  label="Shadow Intensity"
                  value={lightingSettings.shadowIntensity}
                  onChange={(val) => setLightingSettings(p => ({...p, shadowIntensity: val}))}
                />
              )}
            </div>

            <div className="grid grid-cols-2 gap-2 pt-2 border-t border-gray-200 dark:border-gray-700">
              <ColorPicker
                label="Key Light"
                color={lightingSettings.keyLightColor}
                onChange={(color) => setLightingSettings(p => ({...p, keyLightColor: color}))}
              />
              <ColorPicker
                label="Fill Light"
                color={lightingSettings.fillLightColor}
                onChange={(color) => setLightingSettings(p => ({...p, fillLightColor: color}))}
              />
               <ColorPicker
                label="Rim Light"
                color={lightingSettings.rimLightColor}
                onChange={(color) => setLightingSettings(p => ({...p, rimLightColor: color}))}
                />
              </div>
            </div>
          </div>
        )}

      {/* Rendering Settings Panel */}
      {isRenderingMenuOpen && (
        <div
          ref={renderingMenuRef}
          className="absolute top-4 right-24 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg w-72 text-sm text-gray-700 dark:text-gray-300"
        >
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-bold text-lg text-gray-900 dark:text-white">Rendering Settings</h3>
            <button onClick={() => setIsRenderingMenuOpen(false)} className="text-gray-500 hover:text-gray-800 dark:hover:text-white">
              <X size={18} />
            </button>
          </div>

          <div className="space-y-4">
            {/* Anti-aliasing Controls */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium">Anti-aliasing</label>
                <button
                  onClick={() => setRenderingSettings(p => ({...p, enableAntialiasing: !p.enableAntialiasing}))}
                  className={`w-12 h-6 rounded-full transition-colors ${
                    renderingSettings.enableAntialiasing
                      ? 'bg-blue-500'
                      : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                >
                  <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                    renderingSettings.enableAntialiasing ? 'translate-x-6' : 'translate-x-0.5'
                  }`} />
                </button>
              </div>

              {renderingSettings.enableAntialiasing && (
                <div>
                  <label className="block text-xs font-medium mb-2">Quality Level</label>
                  <div className="grid grid-cols-4 gap-1">
                    {[2, 4, 8, 16].map((level) => (
                      <button
                        key={level}
                        onClick={() => setRenderingSettings(p => ({...p, antialiasingLevel: level}))}
                        className={`px-2 py-1 text-xs rounded transition-colors ${
                          renderingSettings.antialiasingLevel === level
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                        }`}
                      >
                        {level}x
                      </button>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Higher values provide smoother edges but may impact performance
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Save As Project Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-6 rounded-lg shadow-lg w-96 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'}`}>
            <h3 className="text-lg font-semibold mb-4">Save As Project</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Project Name</label>
              <input
                type="text"
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
                placeholder="Enter project name..."
                autoFocus
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowSaveDialog(false);
                  setProjectName('');
                }}
                className={`px-4 py-2 rounded-md transition-colors ${
                  isDarkMode
                    ? 'bg-gray-600 hover:bg-gray-700 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                }`}
                disabled={isSaving}
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmSave}
                disabled={!projectName.trim() || isSaving}
                className={`px-4 py-2 rounded-md transition-colors ${
                  !projectName.trim() || isSaving
                    ? 'bg-gray-400 cursor-not-allowed text-gray-600'
                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                }`}
              >
                {isSaving ? 'Saving...' : 'Save Project'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

const LightingSlider = ({ label, value, onChange }: { label: string, value: number, onChange: (value: number) => void }) => (
  <div className="flex flex-col">
    <div className="flex justify-between items-center text-xs">
      <label className="font-medium text-gray-800 dark:text-gray-100">{label}</label>
      <span className="text-gray-600 dark:text-gray-400">{value.toFixed(2)}</span>
    </div>
    <input
      type="range"
      min="0"
      max="3"
      step="0.01"
      value={value}
      onChange={(e) => onChange(parseFloat(e.target.value))}
      className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer"
    />
  </div>
);

const ColorPicker = ({ label, color, onChange }: { label: string, color: string, onChange: (color: string) => void }) => (
  <div className="flex flex-col items-center">
    <label className="text-xs font-medium mb-1 text-gray-800 dark:text-gray-100">{label}</label>
    <input
      type="color"
      value={color}
      onChange={(e) => onChange(e.target.value)}
      className="w-16 h-8 p-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md cursor-pointer"
    />
  </div>
);

export default ModelViewer;
''